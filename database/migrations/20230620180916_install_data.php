<?php

use think\migration\Migrator;
use think\facade\Db;

class InstallData extends Migrator
{
    public string $nowTime = '';

    public function up(): void
    {
        $this->nowTime = time();
        $this->admin();
        $this->adminGroup();
        $this->adminGroupAccess();
        $this->config();
        $this->menuRule();
        $this->securityDataRecycle();
        $this->securitySensitiveData();
        $this->user();
        $this->userGroup();
        $this->userRule();
    }

    public function admin(): void
    {
        $table = $this->table('admin');
        $rows  = [
            [
                'id'         => 1,
                'username'   => 'admin',
                'nickname'   => 'Admin',
                'email'      => '<EMAIL>',
                'mobile'     => '18888888888',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ]
        ];
        $exist = Db::name('admin')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function adminGroup(): void
    {
        $table = $this->table('admin_group');
        $rows  = [
            [
                'id'         => 1,
                'pid'        => 0,
                'name'       => '超级管理组',
                'rules'      => '*',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 2,
                'pid'        => 1,
                'name'       => '一级管理员',
                'rules'      => '1,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,77,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 3,
                'pid'        => 2,
                'name'       => '二级管理员',
                'rules'      => '21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 4,
                'pid'        => 3,
                'name'       => '三级管理员',
                'rules'      => '55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
        ];
        $exist = Db::name('admin_group')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function adminGroupAccess(): void
    {
        $table = $this->table('admin_group_access');
        $rows  = [
            [
                'uid'      => 1,
                'group_id' => 1,
            ]
        ];
        $exist = Db::name('admin_group_access')->where('uid', 1)->value('uid');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function config(): void
    {
        $table = $this->table('config');
        $rows  = [
            [
                'id'    => 1,
                'name'  => 'config_group',
                'group' => 'basics',
                'title' => 'Config group',
                'type'  => 'array',
                'value' => '[{"key":"basics","value":"Basics"},{"key":"mail","value":"Mail"},{"key":"config_quick_entrance","value":"Config Quick entrance"}]',
                'rule'  => 'required',
                'weigh' => -1,
            ],
            [
                'id'    => 2,
                'name'  => 'site_name',
                'group' => 'basics',
                'title' => 'Site Name',
                'tip'   => '',
                'type'  => 'string',
                'value' => '站点名称',
                'rule'  => 'required',
                'weigh' => 99,
            ],
            [
                'id'    => 3,
                'name'  => 'record_number',
                'group' => 'basics',
                'title' => 'Record number',
                'tip'   => '域名备案号',
                'type'  => 'string',
                'value' => '渝ICP备8888888号-1',
            ],
            [
                'id'    => 4,
                'name'  => 'version',
                'group' => 'basics',
                'title' => 'Version number',
                'tip'   => '系统版本号',
                'type'  => 'string',
                'value' => 'v1.0.0',
                'rule'  => 'required',
            ],
            [
                'id'    => 5,
                'name'  => 'time_zone',
                'group' => 'basics',
                'title' => 'time zone',
                'type'  => 'string',
                'value' => 'Asia/Shanghai',
                'rule'  => 'required',
            ],
            [
                'id'    => 6,
                'name'  => 'no_access_ip',
                'group' => 'basics',
                'title' => 'No access ip',
                'tip'   => '禁止访问站点的ip列表,一行一个',
                'type'  => 'textarea',
            ],
            [
                'id'    => 7,
                'name'  => 'smtp_server',
                'group' => 'mail',
                'title' => 'smtp server',
                'type'  => 'string',
                'value' => 'smtp.qq.com',
                'weigh' => 9,
            ],
            [
                'id'    => 8,
                'name'  => 'smtp_port',
                'group' => 'mail',
                'title' => 'smtp port',
                'type'  => 'string',
                'value' => '465',
                'weigh' => 8,
            ],
            [
                'id'    => 9,
                'name'  => 'smtp_user',
                'group' => 'mail',
                'title' => 'smtp user',
                'type'  => 'string',
                'weigh' => 7,
            ],
            [
                'id'    => 10,
                'name'  => 'smtp_pass',
                'group' => 'mail',
                'title' => 'smtp pass',
                'type'  => 'string',
                'weigh' => 6,
            ],
            [
                'id'      => 11,
                'name'    => 'smtp_verification',
                'group'   => 'mail',
                'title'   => 'smtp verification',
                'type'    => 'select',
                'value'   => 'SSL',
                'content' => '{"SSL":"SSL","TLS":"TLS"}',
                'weigh'   => 5,
            ],
            [
                'id'    => 12,
                'name'  => 'smtp_sender_mail',
                'group' => 'mail',
                'title' => 'smtp sender mail',
                'type'  => 'string',
                'rule'  => 'email',
                'weigh' => 4,
            ],
            [
                'id'    => 13,
                'name'  => 'config_quick_entrance',
                'group' => 'config_quick_entrance',
                'title' => 'Config Quick entrance',
                'type'  => 'array',
                'value' => '[{"key":"数据回收规则配置","value":"/admin/security/dataRecycle"},{"key":"敏感数据规则配置","value":"/admin/security/sensitiveData"}]',
            ],
        ];
        $exist = Db::name('config')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function menuRule(): void
    {
        if (!$this->hasTable('menu_rule')) return;
        $table = $this->table('menu_rule');
        $rows  = [
            [
                'id'         => '1',
                'type'       => 'menu',
                'title'      => '控制台',
                'name'       => 'dashboard/dashboard',
                'path'       => 'dashboard',
                'icon'       => 'fa fa-dashboard',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/dashboard.vue',
                'keepalive'  => '1',
                'remark'     => 'Remark lang',
                'weigh'      => '999',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '2',
                'type'       => 'menu_dir',
                'title'      => '权限管理',
                'name'       => 'auth',
                'path'       => 'auth',
                'icon'       => 'fa fa-group',
                'weigh'      => '100',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '3',
                'pid'        => '2',
                'type'       => 'menu',
                'title'      => '角色组管理',
                'name'       => 'auth/group',
                'path'       => 'auth/group',
                'icon'       => 'fa fa-group',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/auth/group/index.vue',
                'keepalive'  => '1',
                'weigh'      => '99',
                'remark'     => 'Remark lang',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '4',
                'pid'        => '3',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'auth/group/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '5',
                'pid'        => '3',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'auth/group/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '6',
                'pid'        => '3',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'auth/group/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '7',
                'pid'        => '3',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'auth/group/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '8',
                'pid'        => '2',
                'type'       => 'menu',
                'title'      => '管理员管理',
                'name'       => 'auth/admin',
                'path'       => 'auth/admin',
                'icon'       => 'el-icon-UserFilled',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/auth/admin/index.vue',
                'keepalive'  => '1',
                'weigh'      => '98',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '9',
                'pid'        => '8',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'auth/admin/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '10',
                'pid'        => '8',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'auth/admin/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '11',
                'pid'        => '8',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'auth/admin/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '12',
                'pid'        => '8',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'auth/admin/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '13',
                'pid'        => '2',
                'type'       => 'menu',
                'title'      => '菜单规则管理',
                'name'       => 'auth/menu',
                'path'       => 'auth/menu',
                'icon'       => 'el-icon-Grid',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/auth/menu/index.vue',
                'keepalive'  => '1',
                'weigh'      => '97',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '14',
                'pid'        => '13',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'auth/menu/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '15',
                'pid'        => '13',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'auth/menu/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '16',
                'pid'        => '13',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'auth/menu/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '17',
                'pid'        => '13',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'auth/menu/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '18',
                'pid'        => '13',
                'type'       => 'button',
                'title'      => '快速排序',
                'name'       => 'auth/menu/sortable',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '19',
                'pid'        => '2',
                'type'       => 'menu',
                'title'      => '管理员日志管理',
                'name'       => 'auth/adminLog',
                'path'       => 'auth/adminLog',
                'icon'       => 'el-icon-List',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/auth/adminLog/index.vue',
                'keepalive'  => '1',
                'weigh'      => '96',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '20',
                'pid'        => '19',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'auth/adminLog/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '21',
                'type'       => 'menu_dir',
                'title'      => '会员管理',
                'name'       => 'user',
                'path'       => 'user',
                'icon'       => 'fa fa-drivers-license',
                'weigh'      => '95',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '22',
                'pid'        => '21',
                'type'       => 'menu',
                'title'      => '会员管理',
                'name'       => 'user/user',
                'path'       => 'user/user',
                'icon'       => 'fa fa-user',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/user/user/index.vue',
                'keepalive'  => '1',
                'weigh'      => '94',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '23',
                'pid'        => '22',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'user/user/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '24',
                'pid'        => '22',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'user/user/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '25',
                'pid'        => '22',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'user/user/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '26',
                'pid'        => '22',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'user/user/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '27',
                'pid'        => '21',
                'type'       => 'menu',
                'title'      => '会员分组管理',
                'name'       => 'user/group',
                'path'       => 'user/group',
                'icon'       => 'fa fa-group',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/user/group/index.vue',
                'keepalive'  => '1',
                'weigh'      => '93',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '28',
                'pid'        => '27',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'user/group/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '29',
                'pid'        => '27',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'user/group/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '30',
                'pid'        => '27',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'user/group/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '31',
                'pid'        => '27',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'user/group/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '32',
                'pid'        => '21',
                'type'       => 'menu',
                'title'      => '会员规则管理',
                'name'       => 'user/rule',
                'path'       => 'user/rule',
                'icon'       => 'fa fa-th-list',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/user/rule/index.vue',
                'keepalive'  => '1',
                'weigh'      => '92',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '33',
                'pid'        => '32',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'user/rule/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '34',
                'pid'        => '32',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'user/rule/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '35',
                'pid'        => '32',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'user/rule/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '36',
                'pid'        => '32',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'user/rule/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '37',
                'pid'        => '32',
                'type'       => 'button',
                'title'      => '快速排序',
                'name'       => 'user/rule/sortable',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '38',
                'pid'        => '21',
                'type'       => 'menu',
                'title'      => '会员余额管理',
                'name'       => 'user/moneyLog',
                'path'       => 'user/moneyLog',
                'icon'       => 'el-icon-Money',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/user/moneyLog/index.vue',
                'keepalive'  => '1',
                'weigh'      => '91',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '39',
                'pid'        => '38',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'user/moneyLog/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '40',
                'pid'        => '38',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'user/moneyLog/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '41',
                'pid'        => '21',
                'type'       => 'menu',
                'title'      => '会员积分管理',
                'name'       => 'user/scoreLog',
                'path'       => 'user/scoreLog',
                'icon'       => 'el-icon-Discount',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/user/scoreLog/index.vue',
                'keepalive'  => '1',
                'weigh'      => '90',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '42',
                'pid'        => '41',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'user/scoreLog/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '43',
                'pid'        => '41',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'user/scoreLog/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '44',
                'type'       => 'menu_dir',
                'title'      => '常规管理',
                'name'       => 'routine',
                'path'       => 'routine',
                'icon'       => 'fa fa-cogs',
                'weigh'      => '89',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '45',
                'pid'        => '44',
                'type'       => 'menu',
                'title'      => '系统配置',
                'name'       => 'routine/config',
                'path'       => 'routine/config',
                'icon'       => 'el-icon-Tools',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/routine/config/index.vue',
                'keepalive'  => '1',
                'weigh'      => '88',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '46',
                'pid'        => '45',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'routine/config/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '47',
                'pid'        => '45',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'routine/config/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '48',
                'pid'        => '44',
                'type'       => 'menu',
                'title'      => '附件管理',
                'name'       => 'routine/attachment',
                'path'       => 'routine/attachment',
                'icon'       => 'fa fa-folder',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/routine/attachment/index.vue',
                'keepalive'  => '1',
                'remark'     => 'Remark lang',
                'weigh'      => '87',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '49',
                'pid'        => '48',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'routine/attachment/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '50',
                'pid'        => '48',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'routine/attachment/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '51',
                'pid'        => '48',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'routine/attachment/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '52',
                'pid'        => '44',
                'type'       => 'menu',
                'title'      => '个人资料',
                'name'       => 'routine/adminInfo',
                'path'       => 'routine/adminInfo',
                'icon'       => 'fa fa-user',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/routine/adminInfo.vue',
                'keepalive'  => '1',
                'weigh'      => '86',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '53',
                'pid'        => '52',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'routine/adminInfo/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '54',
                'pid'        => '52',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'routine/adminInfo/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '55',
                'type'       => 'menu_dir',
                'title'      => '数据安全管理',
                'name'       => 'security',
                'path'       => 'security',
                'icon'       => 'fa fa-shield',
                'weigh'      => '85',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '56',
                'pid'        => '55',
                'type'       => 'menu',
                'title'      => '数据回收站',
                'name'       => 'security/dataRecycleLog',
                'path'       => 'security/dataRecycleLog',
                'icon'       => 'fa fa-database',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/security/dataRecycleLog/index.vue',
                'keepalive'  => '1',
                'weigh'      => '84',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '57',
                'pid'        => '56',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'security/dataRecycleLog/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '58',
                'pid'        => '56',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'security/dataRecycleLog/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '59',
                'pid'        => '56',
                'type'       => 'button',
                'title'      => '还原',
                'name'       => 'security/dataRecycleLog/restore',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '60',
                'pid'        => '56',
                'type'       => 'button',
                'title'      => '查看详情',
                'name'       => 'security/dataRecycleLog/info',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '61',
                'pid'        => '55',
                'type'       => 'menu',
                'title'      => '敏感数据修改记录',
                'name'       => 'security/sensitiveDataLog',
                'path'       => 'security/sensitiveDataLog',
                'icon'       => 'fa fa-expeditedssl',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/security/sensitiveDataLog/index.vue',
                'keepalive'  => '1',
                'weigh'      => '83',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '62',
                'pid'        => '61',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'security/sensitiveDataLog/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '63',
                'pid'        => '61',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'security/sensitiveDataLog/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '64',
                'pid'        => '61',
                'type'       => 'button',
                'title'      => '回滚',
                'name'       => 'security/sensitiveDataLog/rollback',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '65',
                'pid'        => '61',
                'type'       => 'button',
                'title'      => '查看详情',
                'name'       => 'security/sensitiveDataLog/info',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '66',
                'pid'        => '55',
                'type'       => 'menu',
                'title'      => '数据回收规则管理',
                'name'       => 'security/dataRecycle',
                'path'       => 'security/dataRecycle',
                'icon'       => 'fa fa-database',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/security/dataRecycle/index.vue',
                'keepalive'  => '1',
                'remark'     => 'Remark lang',
                'weigh'      => '82',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '67',
                'pid'        => '66',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'security/dataRecycle/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '68',
                'pid'        => '66',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'security/dataRecycle/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '69',
                'pid'        => '66',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'security/dataRecycle/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '70',
                'pid'        => '66',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'security/dataRecycle/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '71',
                'pid'        => '55',
                'type'       => 'menu',
                'title'      => '敏感字段规则管理',
                'name'       => 'security/sensitiveData',
                'path'       => 'security/sensitiveData',
                'icon'       => 'fa fa-expeditedssl',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/security/sensitiveData/index.vue',
                'keepalive'  => '1',
                'remark'     => 'Remark lang',
                'weigh'      => '81',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '72',
                'pid'        => '71',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'security/sensitiveData/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '73',
                'pid'        => '71',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'security/sensitiveData/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '74',
                'pid'        => '71',
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'security/sensitiveData/edit',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '75',
                'pid'        => '71',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'security/sensitiveData/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '76',
                'type'       => 'menu',
                'title'      => 'BuildAdmin',
                'name'       => 'buildadmin/buildadmin',
                'path'       => 'buildadmin',
                'icon'       => 'local-logo',
                'menu_type'  => 'link',
                'url'        => 'https://doc.buildadmin.com',
                'status'     => '0',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '77',
                'pid'        => '45',
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'routine/config/add',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '78',
                'type'       => 'menu',
                'title'      => '模块市场',
                'name'       => 'moduleStore/moduleStore',
                'path'       => 'moduleStore',
                'icon'       => 'el-icon-GoodsFilled',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/module/index.vue',
                'keepalive'  => '1',
                'weigh'      => '86',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '79',
                'pid'        => '78',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'moduleStore/moduleStore/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '80',
                'pid'        => '78',
                'type'       => 'button',
                'title'      => '安装',
                'name'       => 'moduleStore/moduleStore/install',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '81',
                'pid'        => '78',
                'type'       => 'button',
                'title'      => '调整状态',
                'name'       => 'moduleStore/moduleStore/changeState',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '82',
                'pid'        => '78',
                'type'       => 'button',
                'title'      => '卸载',
                'name'       => 'moduleStore/moduleStore/uninstall',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '83',
                'pid'        => '78',
                'type'       => 'button',
                'title'      => '更新',
                'name'       => 'moduleStore/moduleStore/update',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '84',
                'type'       => 'menu',
                'title'      => 'CRUD代码生成',
                'name'       => 'crud/crud',
                'path'       => 'crud/crud',
                'icon'       => 'fa fa-code',
                'menu_type'  => 'tab',
                'component'  => '/src/views/backend/crud/index.vue',
                'keepalive'  => '1',
                'weigh'      => '80',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '85',
                'pid'        => '84',
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'crud/crud/index',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '86',
                'pid'        => '84',
                'type'       => 'button',
                'title'      => '生成',
                'name'       => 'crud/crud/generate',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '87',
                'pid'        => '84',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'crud/crud/delete',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => '88',
                'pid'        => '45',
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'routine/config/del',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],

            // API日志管理菜单
            [
                'id'         => 127,
                'pid'        => 0,
                'type'       => 'menu',
                'title'      => 'API日志管理',
                'name'       => 'api/log',
                'path'       => 'api/log',
                'icon'       => 'iconfont icon-caozuorizhi6',
                'menu_type'  => 'tab',
                'url'        => '',
                'component'  => '/src/views/backend/api/log/index.vue',
                'keepalive'  => 1,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 998,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 128,
                'pid'        => 127,
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'api/log/index',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 129,
                'pid'        => 127,
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'api/log/add',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 130,
                'pid'        => 127,
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'api/log/edit',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 131,
                'pid'        => 127,
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'api/log/del',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 132,
                'pid'        => 127,
                'type'       => 'button',
                'title'      => '快速排序',
                'name'       => 'api/log/sortable',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],

            // 调试日志管理菜单
            [
                'id'         => 134,
                'pid'        => 0,
                'type'       => 'menu',
                'title'      => '调试日志管理',
                'name'       => 'debug/log',
                'path'       => 'debug/log',
                'icon'       => 'iconfont icon-rizhi6',
                'menu_type'  => 'tab',
                'url'        => '',
                'component'  => '/src/views/backend/debug/log/index.vue',
                'keepalive'  => 1,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 997,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 135,
                'pid'        => 134,
                'type'       => 'button',
                'title'      => '查看',
                'name'       => 'debug/log/index',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 136,
                'pid'        => 134,
                'type'       => 'button',
                'title'      => '添加',
                'name'       => 'debug/log/add',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 137,
                'pid'        => 134,
                'type'       => 'button',
                'title'      => '编辑',
                'name'       => 'debug/log/edit',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 138,
                'pid'        => 134,
                'type'       => 'button',
                'title'      => '删除',
                'name'       => 'debug/log/del',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 139,
                'pid'        => 134,
                'type'       => 'button',
                'title'      => '快速排序',
                'name'       => 'debug/log/sortable',
                'path'       => '',
                'icon'       => '',
                'url'        => '',
                'component'  => '',
                'keepalive'  => 0,
                'extend'     => 'none',
                'remark'     => '',
                'weigh'      => 0,
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
        ];
        $exist = Db::name('menu_rule')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function securityDataRecycle(): void
    {
        $table = $this->table('security_data_recycle');
        $rows  = [
            [
                'id'            => 1,
                'name'          => '管理员',
                'controller'    => 'auth/Admin.php',
                'controller_as' => 'auth/admin',
                'data_table'    => 'admin',
                'primary_key'   => 'id',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
            [
                'id'            => 2,
                'name'          => '管理员日志',
                'controller'    => 'auth/AdminLog.php',
                'controller_as' => 'auth/adminlog',
                'data_table'    => 'admin_log',
                'primary_key'   => 'id',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
            [
                'id'            => 3,
                'name'          => '菜单规则',
                'controller'    => 'auth/Menu.php',
                'controller_as' => 'auth/menu',
                'data_table'    => 'menu_rule',
                'primary_key'   => 'id',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
            [
                'id'            => 4,
                'name'          => '系统配置项',
                'controller'    => 'routine/Config.php',
                'controller_as' => 'routine/config',
                'data_table'    => 'config',
                'primary_key'   => 'id',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
            [
                'id'            => 5,
                'name'          => '会员',
                'controller'    => 'user/User.php',
                'controller_as' => 'user/user',
                'data_table'    => 'user',
                'primary_key'   => 'id',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
            [
                'id'            => 6,
                'name'          => '数据回收规则',
                'controller'    => 'security/DataRecycle.php',
                'controller_as' => 'security/datarecycle',
                'data_table'    => 'security_data_recycle',
                'primary_key'   => 'id',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
        ];
        $exist = Db::name('security_data_recycle')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function securitySensitiveData(): void
    {
        $table = $this->table('security_sensitive_data');
        $rows  = [
            [
                'id'            => 1,
                'name'          => '管理员数据',
                'controller'    => 'auth/Admin.php',
                'controller_as' => 'auth/admin',
                'data_table'    => 'admin',
                'primary_key'   => 'id',
                'data_fields'   => '{"username":"用户名","mobile":"手机","password":"密码","status":"状态"}',
                'status'        => '1',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
            [
                'id'            => 2,
                'name'          => '会员数据',
                'controller'    => 'user/User.php',
                'controller_as' => 'user/user',
                'data_table'    => 'user',
                'primary_key'   => 'id',
                'data_fields'   => '{"username":"用户名","mobile":"手机号","password":"密码","status":"状态","email":"邮箱地址"}',
                'status'        => '1',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
            [
                'id'            => 3,
                'name'          => '管理员权限',
                'controller'    => 'auth/Group.php',
                'controller_as' => 'auth/group',
                'data_table'    => 'admin_group',
                'primary_key'   => 'id',
                'data_fields'   => '{"rules":"权限规则ID"}',
                'status'        => '1',
                'updatetime'    => $this->nowTime,
                'createtime'    => $this->nowTime,
            ],
        ];
        $exist = Db::name('security_sensitive_data')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function user(): void
    {
        $table = $this->table('user');
        $rows  = [
            [
                'id'         => 1,
                'group_id'   => 1,
                'username'   => 'user',
                'nickname'   => 'User',
                'email'      => '<EMAIL>',
                'mobile'     => '18888888888',
                'gender'     => '2',
                'birthday'   => date('Y-m-d'),
                'status'     => 'enable',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ]
        ];
        $exist = Db::name('user')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function userGroup(): void
    {
        $table = $this->table('user_group');
        $rows  = [
            [
                'id'         => 1,
                'name'       => '默认分组',
                'rules'      => '*',
                'status'     => '1',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ]
        ];
        $exist = Db::name('user_group')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }

    public function userRule(): void
    {
        $table = $this->table('user_rule');
        $rows  = [
            [
                'id'         => 1,
                'pid'        => 0,
                'type'       => 'menu_dir',
                'title'      => '我的账户',
                'name'       => 'account',
                'path'       => 'account',
                'icon'       => 'fa fa-user-circle',
                'menu_type'  => 'tab',
                'weigh'      => '98',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 2,
                'pid'        => 1,
                'type'       => 'menu',
                'title'      => '账户概览',
                'name'       => 'account/overview',
                'path'       => 'account/overview',
                'icon'       => 'fa fa-home',
                'menu_type'  => 'tab',
                'component'  => '/src/views/frontend/user/account/overview.vue',
                'weigh'      => '99',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 3,
                'pid'        => 1,
                'type'       => 'menu',
                'title'      => '个人资料',
                'name'       => 'account/profile',
                'path'       => 'account/profile',
                'icon'       => 'fa fa-user-circle-o',
                'menu_type'  => 'tab',
                'component'  => '/src/views/frontend/user/account/profile.vue',
                'weigh'      => '98',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 4,
                'pid'        => 1,
                'type'       => 'menu',
                'title'      => '修改密码',
                'name'       => 'account/changePassword',
                'path'       => 'account/changePassword',
                'icon'       => 'fa fa-shield',
                'menu_type'  => 'tab',
                'component'  => '/src/views/frontend/user/account/changePassword.vue',
                'weigh'      => '97',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 5,
                'pid'        => 1,
                'type'       => 'menu',
                'title'      => '积分记录',
                'name'       => 'account/integral',
                'path'       => 'account/integral',
                'icon'       => 'fa fa-tag',
                'menu_type'  => 'tab',
                'component'  => '/src/views/frontend/user/account/integral.vue',
                'weigh'      => '96',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ],
            [
                'id'         => 6,
                'pid'        => 1,
                'type'       => 'menu',
                'title'      => '余额记录',
                'name'       => 'account/balance',
                'path'       => 'account/balance',
                'icon'       => 'fa fa-money',
                'menu_type'  => 'tab',
                'component'  => '/src/views/frontend/user/account/balance.vue',
                'weigh'      => '95',
                'updatetime' => $this->nowTime,
                'createtime' => $this->nowTime,
            ]
        ];
        $exist = Db::name('user_rule')->where('id', 1)->value('id');
        if (!$exist) {
            $table->insert($rows)->saveData();
        }
    }
}
