/**
 * 后台公共语言包
 * 覆盖风险：请避免使用页面语言包的目录名、文件名作为翻译 key
 */
export default {
    Balance: '余额',
    Integral: '积分',
    Connection: '连接标识',
    'Database connection': '数据库连接配置标识',
    'Database connection help': '您可以在 config/database.php 内配置多个数据库连接，然后在此处选择它，留空将使用默认连接配置',
    layouts: {
        'Layout configuration': '布局配置',
        'Layout mode': '布局方式',
        default: '默认',
        classic: '经典',
        'Single column': '单栏',
        'Double column': '双栏',
        'overall situation': '全局',
        'Background page switching animation': '后台页面切换动画',
        'Please select an animation name': '请选择动画名称',
        sidebar: '侧边栏',
        'Side menu bar background color': '侧边菜单栏背景色',
        'Side menu text color': '侧边菜单文字颜色',
        'Side menu active item background color': '侧边菜单激活项背景色',
        'Side menu active item text color': '侧边菜单激活项文字色',
        'Show side menu top bar (logo bar)': '显示侧边菜单顶栏(LOGO栏)',
        'Side menu top bar background color': '侧边菜单顶栏背景色',
        'Side menu width (when expanded)': '侧边菜单宽度(展开时)',
        'Side menu default icon': '侧边菜单默认图标',
        'Side menu horizontal collapse': '侧边菜单水平折叠',
        'Side menu accordion': '侧边菜单手风琴',
        'Top bar': '顶栏',
        'Top bar background color': '顶栏背景色',
        'Top bar text color': '顶栏文字色',
        'Background color when hovering over the top bar': '顶栏悬停时背景色',
        'Top bar menu active item background color': '顶栏菜单激活项背景色',
        'Top bar menu active item text color': '顶栏菜单激活项文字色',
        'Are you sure you want to restore all configurations to the default values?': '确定要恢复全部配置到默认值吗？',
        'Restore default': '恢复默认',
        Profile: '个人资料',
        Logout: '注销',
        'Dark mode': '暗黑模式',
        'Exit full screen': '退出全屏',
        'Full screen is not supported': '您的浏览器不支持全屏，请更换浏览器再试~',
        'Member center': '会员中心',
        'Member information': '会员信息',
        'Login to the buildadmin': '登录到 BuildAdmin 开源社区',
        'Please enter buildadmin account name or email': '请输入 BuildAdmin 账户名/邮箱/手机号',
        'Please enter the buildadmin account password': '请输入 BuildAdmin 账户密码',
        Login: '登录',
        Password: '密码',
        Username: '用户名',
        Register: '没有账户？去注册',
    },
    terminal: {
        Source: '源',
        Terminal: '终端',
        'Command run log': '命令运行日志',
        'No mission yet': '还没有任务...',
        'Test command': '测试命令',
        'Install dependent packages': '安装依赖包',
        Republish: '重新发布',
        'Clean up task list': '清理任务列表',
        unknown: '未知',
        'Waiting for execution': '等待执行',
        Connecting: '连接中...',
        Executing: '执行中...',
        'Successful execution': '执行成功',
        'Execution failed': '执行失败',
        'Unknown execution result': '执行结果未知',
        'Are you sure you want to republish?': '确认要重新发布吗？',
        'Failure to execute this command will block the execution of the queue': '本命令执行失败会阻断队列执行',
        'NPM package manager': 'NPM 包管理器',
        'NPM package manager tip': '选择一个可用的包管理器，用于 WEB 终端中 npm install 等命令的执行',
        'Clear successful task': '清理成功任务',
        'Clear successful task tip': '开始一个新任务时，自动清理列表中已经成功的任务',
        'Manual execution': '手动执行',
        'Do not refresh the browser': '请勿刷新浏览器',
        'Terminal settings': '终端设置',
        'Back to terminal': '回到终端',
        or: '或',
        'Site domain name': '站点域名',
        'The current terminal is not running under the installation service, and some commands may not be executed':
            '当前终端未运行于安装服务下，部分命令可能无法执行。',
        'Newly added tasks will never start because they are blocked by failed tasks': '新添加的任务永远不会开始，因为被失败的任务阻塞！（WEB终端）',
        'Failed to modify the source command, Please try again manually': '修改源的命令执行失败，请手动重试。',
    },
    vite: {
        Later: '稍后',
        'Restart hot update': '重启热更新',
        'Close type terminal': 'WEB终端执行命令',
        'Close type crud': 'CRUD代码生成服务',
        'Close type modules': '模块安装服务',
        'Close type config': '修改系统配置',
        'Reload hot server title': '需要重启 Vite 热更新服务',
        'Reload hot server tips 1': '为确保',
        'Reload hot server tips 2':
            '不被打断，系统暂停了 Vite 的热更新功能，期间前端文件变动将不会实时更新和自动重载网页，现检测到服务暂停期间存在文件更新，需要重启热更新服务。',
        'Reload hot server tips 3': '热更新暂停不影响已经加载好的功能，您可以继续操作，并在一切就绪后再点击重新启动热更新服务。',
    },
}
