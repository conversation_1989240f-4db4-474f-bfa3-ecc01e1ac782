export default {
    // 公共
    'common/sign_name': '短信签名',
    'common/signature': '短信签名',
    // 阿里云
    'aliyun/access_key_id': '阿里云 AccessKey ID',
    'aliyun/access_key_secret': '阿里云 AccessKey Secret',
    'aliyun/placeholder/access_key_id': '请进入阿里云控制台，然后在个人中心->AccessKey管理中查询',
    'aliyun/placeholder/access_key_secret': '请进入阿里云控制台，然后在个人中心->AccessKey管理中查询',
    // 腾讯云
    'qcloud/sdk_app_id': '短信应用的 SDK APP ID',
    'qcloud/secret_id': '腾讯云账户 SecretId',
    'qcloud/secret_key': '腾讯云账户 SecretKey',
    'qcloud/placeholder/sdk_app_id': '请在腾讯云短信->应用管理->应用列表查询',
    'qcloud/placeholder/secret_id': '请在腾讯云个人中心->访问管理->访问密匙->API密匙管理中查询',
    'qcloud/placeholder/secret_key': '请在腾讯云个人中心->访问管理->访问密匙->API密匙管理中查询',
    // 七牛云
    'qiniu/access_key': '七牛云 AccessKey',
    'qiniu/secret_key': '七牛云 SecretKey',
    'qiniu/placeholder/access_key': '请进入七牛云控制台，然后在个人中心->密匙管理中查询',
    'qiniu/placeholder/secret_key': '请进入七牛云控制台，然后在个人中心->密匙管理中查询',
    // 云片
    'yunpian/api_key': 'APIKEY',
    'yunpian/placeholder/api_key': '请进入云片后台->账户设置->子账户管理查询',
    // 阿里云国际
    'aliyunintl/access_key_id': '阿里云 AccessKey ID',
    'aliyunintl/access_key_secret': '阿里云 AccessKey Secret',
    'aliyunintl/placeholder/access_key_id': '请进入阿里云控制台，然后在个人中心->AccessKey管理中查询',
    'aliyunintl/placeholder/access_key_secret': '请进入阿里云控制台，然后在个人中心->AccessKey管理中查询',
    //
    aliyun: '阿里云',
    qcloud: '腾讯云',
    qiniu: '七牛云',
    yunpian: '云片',
    aliyunintl: '阿里云国际',
    'The selected service provider needs to configure information at the bottom of this page (required)':
        '已经选择的服务商需在本页下方配置信息(必须)',
    'Basic SMS configuration': '短信基本配置',
    'Send timeout (seconds)': '发送超时(秒)',
    'Send Policy': '发送策略',
    'Sequential service provider sends': '顺序服务商发送',
    'Sent with the service provider': '随机服务商发送',
    'Available service providers': '可用服务商',
    'Service Provider Configuration': '服务商配置',
    'Service provider': '服务商',
    'Select a service provider to start configuration': '选择一个服务商以开始配置',
}
