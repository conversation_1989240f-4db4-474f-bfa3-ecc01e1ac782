export default {
    id: 'ID',
    uid: '用户ID',
    username: '账号',
    password: '密码',
    paymentPassword: '支付密码',
    sessionId: '会话ID',
    token: 'Token',
    createTime: '注册时间',
    source: '来源',
    remark: '备注',
    status: '状态',
    'status 0': '禁用',
    'status 1': '正常',
    create_time: '创建时间',
    update_time: '修改时间',
    proxyip_id: '代理IP',
    proxyip__proxy: '代理IP',
    'quick Search Fields': 'ID、账号、来源、备注',
}
