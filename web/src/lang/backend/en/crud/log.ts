export default {
    id: 'id',
    table_name: 'name',
    comment: 'comment',
    table: 'table',
    fields: 'fields',
    sync: 'sync',
    'sync no': 'no',
    'sync yes': 'yes',
    status: 'status',
    delete: 'delete code',
    'status delete': 'status delete',
    'status success': 'status success',
    'status error': 'status error',
    'status start': 'status start',
    create_time: 'create_time',
    'quick Search Fields': 'id,table_name,comment',
    'Upload the selected design records to the cloud for cross-device use': 'Upload the selected design records to the cloud for cross-device use',
    'Design records that have been synchronized to the cloud': 'Design records that have been synchronized to the cloud',
    'Cloud record': 'Cloud record',
    Settings: 'Settings',
    'Login for backup design': 'Login for backup design',
    'CRUD design record synchronization scheme': 'CRUD design record synchronization scheme',
    Manual: 'Manual',
    automatic: 'automatic',
    'When automatically synchronizing records, share them to the open source community':
        'When automatically synchronizing records, share them to the open source community',
    'Not to share': 'Not to share',
    Share: 'Share',
    'Enabling sharing can automatically earn community points during development':
        'Enabling sharing can automatically earn community points during development',
    'The synchronized CRUD records are automatically resynchronized when they are updated':
        'The synchronized CRUD records are automatically resynchronized when they are updated',
    'Do not resynchronize': 'Do not resynchronize',
    'Automatic resynchronization': 'Automatic resynchronization',
    'No effective design': 'No effective design',
    'Number of fields': 'Number of fields',
    'Upload type': 'Upload type',
    Update: 'Update',
    'New added': 'New added',
    'Share to earn points': 'Share to earn points',
    'Share to the open source community': 'Share to the open source community',
    'No design record': 'No design record',
    Field: 'Field',
    'Field information': 'Field information',
    'No field': 'No field',
    'Field name': 'Field name',
    Note: 'Note',
    Type: 'Type',
    Load: 'Load',
    'Delete cloud records?': 'Delete cloud records?',
    'You can use the synchronized design records across devices': 'You can use the synchronized design records across devices',
}
