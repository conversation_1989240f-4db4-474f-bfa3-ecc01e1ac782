export default {
    // 公共
    'common/sign_name': 'sign name',
    'common/signature': 'signature',
    // 阿里云
    'aliyun/access_key_id': 'AccessKey ID',
    'aliyun/access_key_secret': 'AccessKey Secret',
    'aliyun/placeholder/access_key_id': 'AccessKey ID',
    'aliyun/placeholder/access_key_secret': 'AccessKey Secret',
    // 腾讯云
    'qcloud/sdk_app_id': 'SDK APP ID',
    'qcloud/secret_id': 'SecretId',
    'qcloud/secret_key': 'SecretKey',
    'qcloud/placeholder/sdk_app_id': 'SDK APP ID',
    'qcloud/placeholder/secret_id': 'SecretId',
    'qcloud/placeholder/secret_key': 'SecretKey',
    // 七牛云
    'qiniu/access_key': 'AccessKey',
    'qiniu/secret_key': 'SecretKey',
    'qiniu/placeholder/access_key': 'AccessKey',
    'qiniu/placeholder/secret_key': 'SecretKey',
    // 云片
    'yunpian/api_key': 'APIKEY',
    'yunpian/placeholder/api_key': 'APIKEY',
    // 阿里云国际
    'aliyunintl/access_key_id': 'AccessKey ID',
    'aliyunintl/access_key_secret': 'AccessKey Secret',
    'aliyunintl/placeholder/access_key_id': 'AccessKey ID',
    'aliyunintl/placeholder/access_key_secret': 'AccessKey Secret',
    //
    aliyun: 'aliyun',
    qcloud: 'qcloud',
    qiniu: 'qiniu',
    yunpian: 'yunpian',
    aliyunintl: 'aliyunintl',
    'The selected service provider needs to configure information at the bottom of this page (required)':
        'The selected service provider needs to configure information at the bottom of this page (required)',
    'Basic SMS configuration': 'Basic SMS configuration',
    'Send timeout (seconds)': 'Send timeout (seconds)',
    'Send Policy': 'Send Policy',
    'Sequential service provider sends': 'Sequential service provider sends',
    'Sent with the service provider': 'Sent with the service provider',
    'Available service providers': 'Available service providers',
    'Service Provider Configuration': 'Service Provider Configuration',
    'Service provider': 'Service provider',
    'Select a service provider to start configuration': 'Select a service provider to start configuration',
}
