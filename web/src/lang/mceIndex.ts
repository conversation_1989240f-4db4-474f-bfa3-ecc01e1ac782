import { useConfig } from '/@/stores/config'

export async function loadMceLang() {
    const config = useConfig()
    const locale = config.lang.defaultLang

    // 按需加载语言包文件的句柄
    if (locale == 'zh-cn') {
        window.loadLangHandle = {
            ...import.meta.glob('./backend/zh-cn/**/*.ts'),
            ...import.meta.glob('./frontend/zh-cn/**/*.ts'),
            ...import.meta.glob('./userend/zh-cn/**/*.ts'),
            ...import.meta.glob('./backend/zh-cn.ts'),
            ...import.meta.glob('./frontend/zh-cn.ts'),
            ...import.meta.glob('./userend/zh-cn.ts'),
        }
    } else {
        window.loadLangHandle = {
            ...import.meta.glob('./backend/en/**/*.ts'),
            ...import.meta.glob('./frontend/en/**/*.ts'),
            ...import.meta.glob('./userend/en/**/*.ts'),
            ...import.meta.glob('./backend/en.ts'),
            ...import.meta.glob('./frontend/en.ts'),
            ...import.meta.glob('./userend/en.ts'),
        }
    }
}
