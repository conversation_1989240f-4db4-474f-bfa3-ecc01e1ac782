import type { RouteRecordRaw } from 'vue-router'

/**
 * 用户扩展基础路由路径
 * 您可以随时于后台->系统配置中修改此值，程序可自动完成代码修改，同时建立对应的API入口和禁止mce应用访问
 */
export const mceBaseRoutePath = '/mce'

/*
 * 用户扩展基础静态路由
 */
const mceBaseRoute: RouteRecordRaw = {
    path: mceBaseRoutePath,
    name: 'mce',
    component: () => import('/@/layouts/userend/index.vue'),
    // 直接重定向到 loading 路由
    redirect: mceBaseRoutePath + '/loading',
    meta: {
        title: `pagesTitle.mce`,
    },
    children: [
        {
            path: 'loading/:to?',
            name: 'mceMainLoading',
            component: () => import('/@/layouts/userend/components/loading.vue'),
            meta: {
                title: `pagesTitle.loading`,
            },
        },
    ],
}

export default mceBaseRoute
