import type { RouteRecordRaw } from 'vue-router'
import { mceBaseRoutePath } from '/@/router/static/mceBase'

const mceStaticRoute: RouteRecordRaw = {
    // 扩展会员中心找不到页面了
    path: mceBaseRoutePath + ':path(.*)*',
    name: 'mceNoFound',
    redirect: (to) => {
        return {
            name: 'mceMainLoading',
            params: {
                to: JSON.stringify({
                    path: to.path,
                    query: to.query,
                }),
            },
        }
    },
}

export default mceStaticRoute
