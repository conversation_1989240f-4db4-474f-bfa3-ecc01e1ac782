<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据导出"
    width="550px"
    :close-on-click-modal="false"
    :show-close="exportStatus === 'options'"
    :close-on-press-escape="exportStatus === 'options'"
    @close="closeDialog"
  >
    <!-- 导出选项 -->
    <div v-if="exportStatus === 'options'">
      <!-- 文件名 -->
      <div class="export-filename">
        <div class="label">文件名：</div>
        <el-input v-model="filename" placeholder="请输入导出文件名" maxlength="50" />
      </div>
      
      <!-- 列选择区域 -->
      <div class="export-columns">
        <div class="columns-header">
          <div class="columns-title">选择导出字段：</div>
          <el-checkbox v-model="selectAll" @change="handleSelectAllChange">全选</el-checkbox>
        </div>
        
        <div class="columns-grid">
          <el-checkbox 
            v-for="column in availableColumns" 
            :key="column.prop"
            v-model="selectedColumns"
            :label="column.prop"
            :disabled="column.required"
          >
            <el-tooltip 
              v-if="column.label.length > 10" 
              :content="column.label" 
              placement="top"
            >
              <div class="column-label">{{ column.label }}</div>
            </el-tooltip>
            <div v-else class="column-label">{{ column.label }}</div>
          </el-checkbox>
        </div>
      </div>
    </div>
    
    <!-- 导出进度 -->
    <div v-else class="export-progress">
      <!-- 状态圆环 -->
      <div class="progress-circle" :style="{
        background: progressStyle.background,
        borderColor: progressStyle.borderColor
      }">
        <span class="progress-text" :style="{ color: progressStyle.textColor }">
          <template v-if="exportStatus === 'completed'"><span style="font-size: 36px; font-weight: bold;">✓</span></template>
          <template v-else>{{ `${progress}%` }}</template>
        </span>
      </div>
      
      <!-- 状态文本 -->
      <div class="status-text" :style="{ color: statusTextColor }">
        {{ statusText }}
      </div>
      
      <!-- 消息文本 -->
      <div class="export-message" :style="{
        backgroundColor: messageStyle.bgColor,
        color: messageStyle.color
      }">
        {{ messageText }}

      </div>
    </div>
    
    <!-- 底部按钮 -->
    <template #footer>
      <span v-if="exportStatus === 'options'" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="startExport" :loading="loading">开始导出</el-button>
      </span>
      <span v-else-if="exportStatus === 'completed'" class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
        <el-button type="primary" @click="handleDownload">下载文件</el-button>
      </span>
      <span v-else class="dialog-footer">
        <el-button @click="closeDialog">取消导出</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import createAxios from '/@/utils/axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';

const route = useRoute();

const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  statusUrl: {
    type: String,
    default: ''
  },
  columns: {
    type: Array,
    required: true
  },
  filter: {
    type: Object,
    default: () => ({})
  },
  defaultFilename: {
    type: String,
    default: '导出文件'
  }
});

const emit = defineEmits(['close', 'success']);

// 导出选项相关状态
const dialogVisible = ref(false);
const loading = ref(false);
const filename = ref(props.defaultFilename);
const selectedColumns = ref<string[]>([]);
const selectAll = ref(false);
const exportId = ref('');

// 当前页面路径
const currentPath = ref('');
// 实际使用的导出URL和状态URL
const actualExportUrl = computed(() => {
  // 如果提供了完整的导出URL，则直接使用
  if (props.url && props.url.includes('/export_data')) {
    return props.url;
  }
  
  // 如果提供了基础URL，则拼接导出方法
  if (props.url) {
    return props.url.endsWith('/') ? `${props.url}export_data` : `${props.url}/export_data`;
  }
  
  // 默认使用当前路径
  return `${currentPath.value}/export_data`;
});

const actualStatusUrl = computed(() => {
  // 如果提供了完整的状态URL，则直接使用
  if (props.statusUrl) {
    return props.statusUrl;
  }
  
  // 如果提供了URL，则基于URL生成状态检查URL
  if (props.url) {
    const baseUrl = props.url.endsWith('/') ? props.url.slice(0, -1) : props.url;
    return `${baseUrl}/check_export_status`;
  }
  
  // 默认使用当前路径
  return `${currentPath.value}/check_export_status`;
});

// 在组件挂载时获取当前页面路径
onMounted(() => {
  // 获取当前页面路径，移除查询参数
  const path = route.path;
  // 如果路径以/结尾，移除结尾的/
  currentPath.value = path.endsWith('/') ? path.slice(0, -1) : path;
});

// 导出进度相关状态
const exportStatus = ref('options');  // options, queued, processing, completed, failed
const progress = ref(0);
const exportFilename = ref('');
const exportedRows = ref(0);
const downloadUrl = ref('');

// 检查定时器
let checkInterval: any = null;

// 计算属性
const availableColumns = computed(() => {
  return props.columns.filter((col: any) => 
    col.prop && 
    !['selection', 'buttons'].includes(col.type) && 
    col.prop !== 'operate'
  ).map((col: any) => ({
    label: col.label || col.prop,
    prop: col.prop,
    required: col.required || false
  }));
});

// 初始化已选列
watch(() => props.columns, () => {
  // 默认选择所有列
  selectedColumns.value = availableColumns.value
    .filter((col: any) => !col.hide)
    .map((col: any) => col.prop);
  
  // 更新全选状态
  updateSelectAllState();
}, { immediate: true });

// 进度条样式
const progressStyle = computed(() => {
  if (exportStatus.value === 'completed') {
    return {
      background: `#ffffff`,  // 改为白色背景
      borderColor: '#67C23A',
      textColor: '#67C23A'
    };
  } else {
    const degrees = (progress.value / 100) * 360;
    return {
      background: `conic-gradient(#409eff ${degrees}deg, #ebeef5 0deg)`,
      borderColor: '#409eff',
      textColor: '#409eff'
    };
  }
});

// 状态文本颜色
const statusTextColor = computed(() => {
  if (exportStatus.value === 'completed') return '#67C23A';
  if (exportStatus.value === 'queued') return '#E6A23C';
  return '#409EFF';
});

// 状态文本
const statusText = computed(() => {
  switch (exportStatus.value) {
    case 'queued': return '等待处理';
    case 'processing': return '正在导出';
    case 'completed': return '导出完成';
    default: return '准备导出';
  }
});

// 消息文本
const messageText = computed(() => {
  switch (exportStatus.value) {
    case 'queued': return '正在队列中等待处理...';
    case 'processing': return `正在处理数据，已完成 ${progress.value}%...`;
    case 'completed': return `共导出 ${exportedRows.value} 条数据`;
    default: return '';
  }
});

// 消息样式
const messageStyle = computed(() => {
  switch (exportStatus.value) {
    case 'queued': return { bgColor: '#fdf6ec', color: '#E6A23C' };
    case 'processing': return { bgColor: '#ecf5ff', color: '#409EFF' };
    case 'completed': return { bgColor: '#f0f9eb', color: '#67C23A' };
    default: return { bgColor: '#f5f7fa', color: '#909399' };
  }
});

// 方法
function updateSelectAllState() {
  // 检查是否所有非必选列都已选中
  const availableColumnProps = availableColumns.value
    .filter((col: any) => !col.required)
    .map((col: any) => col.prop);
  
  const allSelected = availableColumnProps.every(prop => 
    selectedColumns.value.includes(prop)
  );
  
  selectAll.value = allSelected && availableColumnProps.length > 0;
}

function handleSelectAllChange(val: boolean) {
  if (val) {
    // 选择所有列
    selectedColumns.value = availableColumns.value.map(col => col.prop);
  } else {
    // 只保留必选列
    selectedColumns.value = availableColumns.value
      .filter(col => col.required)
      .map(col => col.prop);
  }
}

// 监听选择变化以更新全选状态
watch(selectedColumns, () => {
  updateSelectAllState();
});

async function startExport() {
  if (selectedColumns.value.length === 0) {
    ElMessage.warning('请至少选择一个导出列');
    return;
  }

  if (!filename.value) {
    ElMessage.warning('请输入文件名');
    return;
  }

  loading.value = true;

  try {
    // 发起导出请求
    const result = await createAxios({
      url: actualExportUrl.value,
      method: 'post',
      params: {
        ...props.filter
      },
      data: {
        columns: selectedColumns.value,
        filename: filename.value
      }
    });

    if (result.code === 1) {
      // 设置导出ID
      exportId.value = result.data.export_id;
      exportStatus.value = 'queued';
      
      // 启动状态检查
      startStatusCheck();
    } else {
      ElMessage.error(result.msg || '导出失败');
      reset();
    }
  } catch (error) {
    console.error('Export error:', error);
    ElMessage.error('导出请求失败，请稍后重试');
    reset();
  }
}

function startStatusCheck() {
  // 清除之前的定时器
  if (checkInterval) {
    clearInterval(checkInterval);
  }

  // 设置新的定时器，每2秒检查一次
  checkInterval = setInterval(checkExportStatus, 2000);
}

async function checkExportStatus() {
  if (!exportId.value) {
    clearInterval(checkInterval);
    return;
  }

  try {
    const result = await createAxios({
      url: actualStatusUrl.value,
      method: 'get',
      params: {
        export_id: exportId.value
      }
    });

    if (result.code === 1) {
      const status = result.data.status;
      progress.value = result.data.progress || 0;
      exportedRows.value = result.data.rows || 0;
      
      // 更新状态
      exportStatus.value = status;
      
      // 如果有文件名，则更新
      if (result.data.filename) {
        exportFilename.value = result.data.filename;
      } else {
        exportFilename.value = '导出文件名';
      }
      
      // 如果有下载链接，则更新
      if (result.data.download_url) {
        downloadUrl.value = result.data.download_url;
        
        // 如果已完成，则停止检查
        if (status === 'completed') {
          clearInterval(checkInterval);
          loading.value = false;
        }
      }
      
      // 如果失败或完成，则停止检查
      if (status === 'failed') {
        clearInterval(checkInterval);
        loading.value = false;
        ElMessage.error('导出失败，请稍后重试');
      }
    } else {
      clearInterval(checkInterval);
      loading.value = false;
      ElMessage.error(result.msg || '检查导出状态失败');
    }
  } catch (error) {
    console.error('Check export status error:', error);
    clearInterval(checkInterval);
    loading.value = false;
    ElMessage.error('检查导出状态失败，请稍后重试');
  }
}

function handleDownload() {
  if (downloadUrl.value) {
    window.open(downloadUrl.value, '_blank');
  }
}

function reset() {
  // 清除定时器
  if (checkInterval) {
    clearInterval(checkInterval);
    checkInterval = null;
  }
  
  // 重置状态
  loading.value = false;
  exportStatus.value = 'options';
  progress.value = 0;
  exportFilename.value = '';
  exportedRows.value = 0;
  downloadUrl.value = '';
  exportId.value = '';
}

function closeDialog() {
  dialogVisible.value = false;
  reset();
  emit('close');
}

function open() {
  // 重置状态
  reset();
  
  // 重置文件名为默认值
  filename.value = props.defaultFilename;
  
  // 默认选择所有列
  selectedColumns.value = availableColumns.value
    .filter((col: any) => !col.hide)
    .map((col: any) => col.prop);
  
  // 更新全选状态
  updateSelectAllState();
  
  // 显示对话框
  dialogVisible.value = true;
}

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.export-filename {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.export-filename .label {
  width: 70px;
  padding-right: 10px;
}

.export-columns {
  margin-bottom: 10px;
}

.columns-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.columns-title {
  font-weight: bold;
}

.columns-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.column-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.export-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.progress-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 3px solid #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  position: relative;  // 添加定位
  overflow: visible;   // 确保内容不会被裁剪
}

.progress-text {
  font-size: 18px;
  font-weight: bold;
  position: relative;  // 添加定位
  z-index: 2;          // 确保文本在最上层
}

.status-text {
  font-size: 16px;
  margin-bottom: 15px;
}

.export-message {
  margin-top: 15px;
  font-size: 13px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.download-btn {
  margin-left: 10px;
}
</style> 