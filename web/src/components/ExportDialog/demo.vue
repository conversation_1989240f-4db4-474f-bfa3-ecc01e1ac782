<template>
    <div class="default-main ba-table-box">
        <!-- 表格顶部菜单 -->
        <!-- 自定义按钮请使用插槽，甚至公共搜索也可以使用具名插槽渲染，参见文档 -->
        <TableHeader>
            <el-button v-blur class="table-header-operate" style="background-color: #640be3; color: #FFFFFF;margin-left: 12px" @click="$refs.exportDialog.open()" v-auth="'export_data'">
                <Icon name="iconfont icon-daochu" />
                <i class="el-icon-delete" style="color: #ffffff;"></i>
                <span class="table-header-operate-text">导出数据</span>
            </el-button>
        </TableHeader>

        <!-- 导出对话框 -->
        <ExportDialog
            ref="exportDialog"
            url="/admin/sams.User/"
            :columns="baTable?.table?.column || []"
            :filter="baTable?.table?.filter || {}"
            defaultFilename="导出数据"
        />
    </div>
</template>
<script setup lang="ts">
import ExportDialog from '/@/components/ExportDialog/index.vue'
</script>
