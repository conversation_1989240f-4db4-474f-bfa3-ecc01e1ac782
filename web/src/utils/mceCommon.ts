import { mceBaseRoutePath } from '/@/router/static/mceBase'
import { getCurrentRoutePath, isAdminApp } from '/@/utils/common'

import { useNavTabs } from '/@/stores/navTabs'
import { useMemberCenter } from '/@/stores/memberCenter'
import { useMceNavTabs } from '/@/stores/mceNavTabs'

/**
 * 是否在用户扩展应用内
 * @param path 不传递则通过当前路由 path 检查
 */
export const isMceApp = (path = '') => {
    const regex = new RegExp(`^${mceBaseRoutePath}`)
    if (path) {
        return regex.test(path)
    }
    if (regex.test(getCurrentRoutePath())) {
        return true
    }
    return false
}

/**
 * 鉴权
 * 提供 string 将根据当前路由 path 自动拼接和鉴权，还可以提供路由的 name 对象进行鉴权
 * @param node
 */
export function auth(node: string | { name: string; subNodeName?: string }) {
    const store = isAdminApp() ? useNavTabs() : isMceApp() ? useMceNavTabs() : useMemberCenter()
    if (typeof node === 'string') {
        const path = getCurrentRoutePath()
        if (store.state.authNode.has(path)) {
            const subNodeName = path + (path == '/' ? '' : '/') + node
            if (store.state.authNode.get(path)!.some((v: string) => v == subNodeName)) {
                return true
            }
        }
    } else {
        // 节点列表中没有找到 name
        if (!node.name || !store.state.authNode.has(node.name)) return false

        // 无需继续检查子节点或未找到子节点
        if (!node.subNodeName || store.state.authNode.get(node.name)?.includes(node.subNodeName)) return true
    }
    return false
}
