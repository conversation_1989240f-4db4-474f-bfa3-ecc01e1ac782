<template>
    <el-container class="layout-container">
        <Aside />
        <el-container class="content-wrapper">
            <Header />
            <Main />
        </el-container>
    </el-container>
    <CloseFullScreen v-if="navTabs.state.tabFullScreen" />
</template>

<script setup lang="ts">
import Aside from '/@/layouts/userend/components/aside.vue'
import Header from '/@/layouts/userend/components/header.vue'
import Main from '/@/layouts/userend/router-view/main.vue'
import CloseFullScreen from '/@/layouts/userend/components/closeFullScreen.vue'
import { useMceNavTabs } from '/@/stores/mceNavTabs'
const navTabs = useMceNavTabs()
</script>

<style scoped>
.layout-container {
    height: 100%;
    width: 100%;
}
.content-wrapper {
    flex-direction: column;
    width: 100%;
    height: 100%;
}
</style>
