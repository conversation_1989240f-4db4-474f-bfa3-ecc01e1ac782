<template>
    <el-main class="layout-main">
        <router-view v-slot="{ Component }">
            <transition :name="config.layout.mainAnimation" mode="out-in">
                <component :is="Component" />
            </transition>
        </router-view>
    </el-main>
</template>

<script setup lang="ts">
import { useConfig } from '/@/stores/config'

const config = useConfig()
</script>

<style scoped lang="scss">
.layout-main {
    padding: 0 !important;
    overflow-x: hidden;
    flex: 1;
    width: calc(100% - 240px);
}

@media screen and (max-width: 991px) {
    .layout-main {
        width: 100%;
    }
}
</style>
