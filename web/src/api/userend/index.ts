import { useUserInfo } from '/@/stores/mceInfo'
import createAxios from '/@/utils/mceAxios'

export const url = '/mce/Index/'

export function index() {
    return createAxios({
        url: url + 'index',
        method: 'get',
    })
}

export function login(method: 'get' | 'post', params: object = {}) {
    return createAxios({
        url: url + 'login',
        data: params,
        method: method,
    })
}

export function logout() {
    const userInfo = useUserInfo()
    return createAxios({
        url: url + 'logout',
        method: 'POST',
        data: {
            refreshToken: userInfo.getToken('refresh'),
        },
    })
}
