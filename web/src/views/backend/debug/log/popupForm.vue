<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('debug.log.level')"
                        type="string"
                        v-model="baTable.form.items!.level"
                        prop="level"
                        :placeholder="t('Please input field', { field: t('debug.log.level') })"
                    />
                    <FormItem
                        :label="t('debug.log.tag')"
                        type="string"
                        v-model="baTable.form.items!.tag"
                        prop="tag"
                        :placeholder="t('Please input field', { field: t('debug.log.tag') })"
                    />
                    <FormItem
                        :label="t('debug.log.module')"
                        type="string"
                        v-model="baTable.form.items!.module"
                        prop="module"
                        :placeholder="t('Please input field', { field: t('debug.log.module') })"
                    />
                    <FormItem
                        :label="t('debug.log.function')"
                        type="string"
                        v-model="baTable.form.items!.function"
                        prop="function"
                        :placeholder="t('Please input field', { field: t('debug.log.function') })"
                    />
                    <FormItem
                        :label="t('debug.log.line')"
                        type="number"
                        v-model="baTable.form.items!.line"
                        prop="line"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('debug.log.line') })"
                    />
                    <FormItem
                        :label="t('debug.log.message')"
                        type="textarea"
                        v-model="baTable.form.items!.message"
                        prop="message"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('debug.log.message') })"
                    />
                    <FormItem
                        :label="t('debug.log.context')"
                        type="textarea"
                        v-model="baTable.form.items!.context"
                        prop="context"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('debug.log.context') })"
                    />
                    <FormItem
                        :label="t('debug.log.trace')"
                        type="textarea"
                        v-model="baTable.form.items!.trace"
                        prop="trace"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('debug.log.trace') })"
                    />
                    <FormItem
                        :label="t('debug.log.request_id')"
                        type="string"
                        v-model="baTable.form.items!.request_id"
                        prop="request_id"
                        :placeholder="t('Please input field', { field: t('debug.log.request_id') })"
                    />
                    <FormItem
                        :label="t('debug.log.user_id')"
                        type="string"
                        v-model="baTable.form.items!.user_id"
                        prop="user_id"
                        :placeholder="t('Please input field', { field: t('debug.log.user_id') })"
                    />
                    <FormItem
                        :label="t('debug.log.ip')"
                        type="string"
                        v-model="baTable.form.items!.ip"
                        prop="ip"
                        :placeholder="t('Please input field', { field: t('debug.log.ip') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    line: [buildValidatorData({ name: 'number', title: t('debug.log.line') })],
    create_time: [buildValidatorData({ name: 'date', title: t('debug.log.create_time') })],
})
</script>

<style scoped lang="scss"></style>
