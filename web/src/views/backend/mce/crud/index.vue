<template>
    <div>
        <component :is="state.step"></component>
    </div>
</template>

<script setup lang="ts">
import { onActivated, onDeactivated, onUnmounted, onMounted } from 'vue'
import Start from '/@/views/backend/mce/crud/start.vue'
import Design from '/@/views/backend/mce/crud/design.vue'
import { state } from '/@/views/backend/mce/crud/index'
import { closeHotUpdate, openHotUpdate } from '/@/utils/vite'

defineOptions({
    name: 'mce/crud/crud',
    components: { Start, Design },
})

onMounted(() => {
    closeHotUpdate('mce/crud')
})

onActivated(() => {
    closeHotUpdate('mce/crud')
})

onDeactivated(() => {
    openHotUpdate('mce/crud')
})

onUnmounted(() => {
    openHotUpdate('mce/crud')
})
</script>

<style scoped lang="scss"></style>
