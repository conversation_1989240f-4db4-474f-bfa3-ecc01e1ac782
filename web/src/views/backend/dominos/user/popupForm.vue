<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('dominos.user.username')"
                        type="string"
                        v-model="baTable.form.items!.username"
                        prop="username"
                        :placeholder="t('Please input field', { field: t('dominos.user.username') })"
                    />
                    <FormItem
                        :label="t('dominos.user.password')"
                        type="string"
                        v-model="baTable.form.items!.password"
                        prop="password"
                        :placeholder="t('Please input field', { field: t('dominos.user.password') })"
                    />
                    <FormItem
                        :label="t('dominos.user.paymentPassword')"
                        type="string"
                        v-model="baTable.form.items!.paymentPassword"
                        prop="paymentPassword"
                        :placeholder="t('Please input field', { field: t('dominos.user.paymentPassword') })"
                    />
                    <FormItem
                        :label="t('dominos.user.sessionId')"
                        type="string"
                        v-model="baTable.form.items!.sessionId"
                        prop="sessionId"
                        :placeholder="t('Please input field', { field: t('dominos.user.sessionId') })"
                    />
                    <FormItem
                        :label="t('dominos.user.token')"
                        type="string"
                        v-model="baTable.form.items!.token"
                        prop="token"
                        :placeholder="t('Please input field', { field: t('dominos.user.token') })"
                    />
                    <FormItem
                        :label="t('dominos.user.source')"
                        type="string"
                        v-model="baTable.form.items!.source"
                        prop="source"
                        :placeholder="t('Please input field', { field: t('dominos.user.source') })"
                    />
                    <FormItem
                        :label="t('dominos.user.remark')"
                        type="textarea"
                        v-model="baTable.form.items!.remark"
                        prop="remark"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('dominos.user.remark') })"
                    />
                    <FormItem
                        :label="t('dominos.user.status')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :input-attr="{ content: { '0': t('dominos.user.status 0'), '1': t('dominos.user.status 1') } }"
                        :placeholder="t('Please select field', { field: t('dominos.user.status') })"
                    />
                    <FormItem
                        :label="t('dominos.user.proxyip_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.proxyip_id"
                        prop="proxyip_id"
                        :input-attr="{ pk: 'proxyip.id', field: 'proxy', remoteUrl: '/admin/Proxyip/index' }"
                        :placeholder="t('Please select field', { field: t('dominos.user.proxyip_id') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    username: [buildValidatorData({ name: 'required', title: t('dominos.user.username') })],
    password: [buildValidatorData({ name: 'required', title: t('dominos.user.password') })],
    createTime: [buildValidatorData({ name: 'date', title: t('dominos.user.createTime') })],
    status: [buildValidatorData({ name: 'required', title: t('dominos.user.status') })],
    create_time: [buildValidatorData({ name: 'date', title: t('dominos.user.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('dominos.user.update_time') })],
})
</script>

<style scoped lang="scss"></style>
