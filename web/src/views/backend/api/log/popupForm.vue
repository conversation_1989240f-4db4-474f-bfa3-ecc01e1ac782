<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('api.log.method')"
                        type="string"
                        v-model="baTable.form.items!.method"
                        prop="method"
                        :placeholder="t('Please input field', { field: t('api.log.method') })"
                    />
                    <FormItem
                        :label="t('api.log.url')"
                        type="string"
                        v-model="baTable.form.items!.url"
                        prop="url"
                        :placeholder="t('Please input field', { field: t('api.log.url') })"
                    />
                    <FormItem
                        :label="t('api.log.path')"
                        type="string"
                        v-model="baTable.form.items!.path"
                        prop="path"
                        :placeholder="t('Please input field', { field: t('api.log.path') })"
                    />
                    <FormItem
                        :label="t('api.log.query_string')"
                        type="textarea"
                        v-model="baTable.form.items!.query_string"
                        prop="query_string"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('api.log.query_string') })"
                    />
                    <FormItem
                        :label="t('api.log.request_data')"
                        type="textarea"
                        v-model="baTable.form.items!.request_data"
                        prop="request_data"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('api.log.request_data') })"
                    />
                    <FormItem
                        :label="t('api.log.response_code')"
                        type="number"
                        v-model="baTable.form.items!.response_code"
                        prop="response_code"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('api.log.response_code') })"
                    />
                    <FormItem
                        :label="t('api.log.response_data')"
                        type="textarea"
                        v-model="baTable.form.items!.response_data"
                        prop="response_data"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('api.log.response_data') })"
                    />
                    <FormItem
                        :label="t('api.log.error_msg')"
                        type="textarea"
                        v-model="baTable.form.items!.error_msg"
                        prop="error_msg"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('api.log.error_msg') })"
                    />
                    <FormItem
                        :label="t('api.log.execution_time')"
                        type="number"
                        v-model="baTable.form.items!.execution_time"
                        prop="execution_time"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('api.log.execution_time') })"
                    />
                    <FormItem
                        :label="t('api.log.ip')"
                        type="string"
                        v-model="baTable.form.items!.ip"
                        prop="ip"
                        :placeholder="t('Please input field', { field: t('api.log.ip') })"
                    />
                    <FormItem
                        :label="t('api.log.useragent')"
                        type="string"
                        v-model="baTable.form.items!.useragent"
                        prop="useragent"
                        :placeholder="t('Please input field', { field: t('api.log.useragent') })"
                    />
                    <FormItem
                        :label="t('api.log.device_type')"
                        type="string"
                        v-model="baTable.form.items!.device_type"
                        prop="device_type"
                        :placeholder="t('Please input field', { field: t('api.log.device_type') })"
                    />
                    <FormItem
                        :label="t('api.log.status')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :input-attr="{ content: { '0': t('api.log.status 0'), '1': t('api.log.status 1') } }"
                        :placeholder="t('Please select field', { field: t('api.log.status') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    response_code: [buildValidatorData({ name: 'number', title: t('api.log.response_code') })],
    execution_time: [buildValidatorData({ name: 'number', title: t('api.log.execution_time') })],
    create_time: [buildValidatorData({ name: 'date', title: t('api.log.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('api.log.update_time') })],
})
</script>

<style scoped lang="scss"></style>
