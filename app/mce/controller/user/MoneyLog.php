<?php

namespace app\mce\controller\user;

use Throwable;
use app\common\model\User;
use app\mce\model\UserMoneyLog;
use app\common\controller\Userend;

class MoneyLog extends Userend
{
    /**
     * @var object
     * @phpstan-var UserMoneyLog
     */
    protected object $model;

    protected array $withJoinTable = ['user'];

    // 排除字段
    protected array|string $preExcludeFields = ['id', 'create_time'];

    protected string|array $quickSearchField = ['id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new UserMoneyLog();
        $this->request->filter('clean_xss');
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\user\library\traits\Userend 中对应的方法至此进行重写
     */
}