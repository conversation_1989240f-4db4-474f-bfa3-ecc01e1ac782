<?php

namespace app\mce\controller\user;

use app\common\controller\Userend;

/**
 * 用户日志
 */
class Log extends Userend
{
    /**
     * Log模型对象
     * @var object
     * @phpstan-var \app\common\model\mce\UserLog
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time'];

    protected string|array $quickSearchField = ['id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\common\model\mce\UserLog();
        $this->request->filter('clean_xss');
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\user\library\traits\Userend 中对应的方法至此进行重写
     */
}