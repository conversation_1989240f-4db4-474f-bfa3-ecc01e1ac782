<?php

namespace app\mce\controller\user;

use Throwable;
use app\common\model\User;
use app\mce\model\UserScoreLog;
use app\common\controller\Userend;

class ScoreLog extends Userend
{
    /**
     * @var object
     * @phpstan-var UserScoreLog
     */
    protected object $model;

    protected array $withJoinTable = ['user'];

    // 排除字段
    protected array|string $preExcludeFields = ['id', 'create_time'];

    protected string|array $quickSearchField = ['id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new UserScoreLog();
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\user\library\traits\Userend 中对应的方法至此进行重写
     */

}