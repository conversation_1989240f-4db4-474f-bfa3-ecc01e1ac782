<?php
namespace app\admin\traits;

/**
 * 导出功能特性
 * 为控制器提供统一的数据导出功能
 */
trait ExportTrait
{
    /**
     * 导出数据
     * @return \think\Response
     */
    public function export_data()
    {
        // 获取参数
        $columns = $this->request->post('columns', []);
        $filename = $this->request->post('filename', $this->getExportFilename());
        $taskType = $this->request->post('task_type', 'plantask'); // 默认使用计划任务

        // 使用当前页面筛选条件
        list($where, $alias, $limit, $order) = $this->queryBuilder();
        
        // 获取当前模块名称（用于缓存键前缀）
        $module = $this->getExportModuleName();

        // 自动获取表字段、生成表头和特殊字段处理配置
        $exportService = new \app\common\service\ExportService();
        $result = $exportService->initExport(
            get_class($this->model),
            $columns, // 不传或传空数组会自动导出全部字段
            $filename,
            [
                'where' => $where,
                'alias' => $alias,
                'order' => $order
            ],
            [], // 不传表头会自动从表注释生成
            [], // 不传特殊字段处理会自动生成
            $module,
            $taskType
        );

        // 返回导出ID和查询状态
        return json($result);
    }

    /**
     * 检查导出状态
     * @return \think\Response
     */
    public function check_export_status()
    {
        $exportId = $this->request->param('export_id');

        if (empty($exportId)) {
            return $this->error('导出ID不能为空');
        }

        // 获取当前模块名称（用于缓存键前缀）
        $module = $this->getExportModuleName();

        // 实例化导出服务
        $exportService = new \app\common\service\ExportService();

        // 获取导出状态
        $result = $exportService->getExportStatus($exportId, $module);

        if ($result['code'] === 1) {
            return $this->success($result['msg'], $result['data']);
        } else {
            return $this->error($result['msg']);
        }
    }

    /**
     * 下载导出文件
     * @return \think\Response
     */
    public function download_export()
    {
        $exportId = $this->request->param('export_id');

        if (empty($exportId)) {
            return $this->error('导出ID不能为空');
        }

        // 获取当前模块名称（用于缓存键前缀）
        $module = $this->getExportModuleName();

        // 实例化导出服务
        $exportService = new \app\common\service\ExportService();

        // 下载文件
        return $exportService->downloadExport($exportId, $module);
    }

    /**
     * 通用查询构建方法
     * 根据过滤条件构建数据库查询对象
     * 
     * @param array $filter 过滤条件
     * @return \think\db\Query
     */
    public function getQuery($filter = [])
    {
        $query = $this->model;
        
        // 如果模型有alias方法并且是常见的查询场景，默认使用alias('a')
        if (method_exists($query, 'alias')) {
            $query = $query->alias('a');
        }

        // 处理筛选条件
        if (is_array($filter) && !empty($filter)) {
            foreach ($filter as $key => $value) {
                // 跳过空值和分页参数
                if ($value === '' || $value === null || in_array($key, ['page', 'limit', 'sort', 'order'])) {
                    continue;
                }

                // 表别名前缀，如果有别名则添加
                $prefix = '';
                if (method_exists($query, 'getAlias') && $query->getAlias()) {
                    $prefix = $query->getAlias() . '.';
                } elseif (strpos($key, '.') === false) {
                    // 如果字段名中没有.且使用了alias('a')，默认添加a.前缀
                    $prefix = 'a.';
                }

                // 处理特殊操作符
                if (is_array($value) && isset($value['op'])) {
                    switch ($value['op']) {
                        case 'LIKE':
                            $query->where($prefix . $key, 'like', '%' . $value['value'] . '%');
                            break;
                        case 'IN':
                            if (is_string($value['value'])) {
                                $value['value'] = explode(',', $value['value']);
                            }
                            $query->where($prefix . $key, 'in', $value['value']);
                            break;
                        case 'BETWEEN':
                        case 'RANGE':
                            if (is_string($value['value'])) {
                                $range = explode(',', $value['value']);
                            } else {
                                $range = $value['value'];
                            }
                            if (count($range) == 2) {
                                $query->where($prefix . $key, 'between', $range);
                            }
                            break;
                        default:
                            $query->where($prefix . $key, $value['op'], $value['value']);
                            break;
                    }
                } else if (is_array($value)) {
                    // 处理数组值（默认为IN操作）
                    $query->where($prefix . $key, 'in', $value);
                } else {
                    // 处理普通相等查询
                    $query->where($prefix . $key, '=', $value);
                }
            }
        }

        return $query;
    }

    /**
     * 获取导出文件名（可在子类中重写）
     * @return string
     */
    protected function getExportFilename()
    {
        // 尝试从控制器获取更有意义的默认文件名
        $controller = app('http')->getName() . '/' . request()->controller();
        $controllerParts = explode('/', $controller);
        $className = end($controllerParts);
        
        return $className . '数据导出';
    }

    /**
     * 获取导出模块名称（用于缓存键前缀，可在子类中重写）
     * @return string
     */
    protected function getExportModuleName()
    {
        // 默认使用控制器名称作为模块名称
        $controller = app('http')->getName() . '/' . request()->controller();
        $controllerParts = explode('/', $controller);
        $className = end($controllerParts);
        
        // 转换为小写并移除Controller后缀
        $module = strtolower(str_replace('Controller', '', $className));
        
        return $module;
    }
} 