<?php

namespace app\admin\controller\mce\user;

use app\common\controller\Backend;

/**
 * 会员等级
 */
class Level extends Backend
{
    /**
     * Level模型对象
     * @var object
     * @phpstan-var \app\common\model\mce\UserLevel
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id'];

    protected string|array $quickSearchField = ['id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\common\model\mce\UserLevel();
    }


    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}