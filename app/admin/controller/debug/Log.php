<?php

namespace app\admin\controller\debug;

use app\common\controller\Backend;

/**
 * 调试日志管理
 */
class Log extends Backend
{
    /**
     * Log模型对象
     * @var object
     * @phpstan-var \app\common\model\debug\Log
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time'];

    protected string|array $quickSearchField = ['id', 'request_id', 'module', 'function'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\common\model\debug\Log();
    }


    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}