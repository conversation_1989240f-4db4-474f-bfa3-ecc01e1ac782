<?php

use app\common\model\debug\Log as DebugLog;

/**
 * 记录调试日志
 * @param string $message 日志消息
 * @param array|string $context 上下文数据或日志标签
 * @param string $level 日志级别(debug/info/warning/error/critical)
 * @return bool
 */
function debug_log(string $message, $context = [], string $level = 'info'): bool
{
    // 如果context是字符串，则视为tag
    $tag = is_string($context) ? $context : 'default';
    $contextData = is_array($context) ? $context : [];
    
    // 调用日志记录方法，跳过1层堆栈（即当前函数）
    return DebugLog::record($level, $tag, $message, $contextData, 1);
} 