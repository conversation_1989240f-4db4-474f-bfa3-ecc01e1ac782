<?php

namespace app\common\model\api;

use think\Model;
use Throwable;

/**
 * API日志模型
 */
class Log extends Model
{
    // 表名
    protected $name = 'api_log';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    
    // 时间字段
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 数据类型转换
     */
    protected $type = [
        'id'             => 'integer',
        'response_code'  => 'integer',
        'execution_time' => 'float',
        'status'         => 'integer',
        'create_time'    => 'integer',
        'update_time'    => 'integer',
    ];
    
    /**
     * 脱敏的字段正则
     * @var array
     */
    protected array $desensitizationRegex = [
        '/(password|salt|token|secret|key)/i'
    ];

    /**
     * 单例模式获取实例
     * @return static
     */
    public static function instance(): static
    {
        $request = request();
        if (!isset($request->apiLog)) {
            $request->apiLog = new static();
        }
        return $request->apiLog;
    }

    /**
     * 设置需要进行数据脱敏的正则列表
     * @param array|string $regex
     */
    public function setDesensitizationRegex(array|string $regex = []): void
    {
        $regex = is_array($regex) ? $regex : [$regex];
        $this->desensitizationRegex = array_merge($this->desensitizationRegex, $regex);
    }

    /**
     * 数据脱敏（根据数组 key 脱敏）
     * @param array|string $data
     * @return array|string
     */
    protected function desensitization(array|string $data): array|string
    {
        if (!is_array($data) || !$this->desensitizationRegex) {
            return $data;
        }
        foreach ($data as $index => &$item) {
            foreach ($this->desensitizationRegex as $reg) {
                if (preg_match($reg, $index)) {
                    $item = "***";
                } elseif (is_array($item)) {
                    $item = $this->desensitization($item);
                }
            }
        }
        return $data;
    }

    /**
     * 记录API日志
     * @param string $responseContent 响应内容
     * @param int $responseCode 响应状态码
     * @param float $executionTime 执行时间
     * @throws Throwable
     */
    public function record(string $responseContent = '', int $responseCode = 200, float $executionTime = 0): void
    {
        $request = request();
        
        // 获取请求信息
        $method = $request->method();
        $url = $request->url(true);
        $path = str_replace('.', '/', $request->controller(true)) . '/' . $request->action(true);
        
        // 获取请求数据
        $queryString = $request->query() ? json_encode($request->query(), JSON_UNESCAPED_UNICODE) : '';
        $requestData = $request->post() ? json_encode($this->desensitization($request->post()), JSON_UNESCAPED_UNICODE) : '';
        
        // 处理响应数据
        $responseData = '';
        if ($responseContent) {
            try {
                $decoded = json_decode($responseContent, true);
                if (is_array($decoded)) {
                    // 对响应数据进行脱敏处理
                    $decoded = $this->desensitization($decoded);
                    $responseData = json_encode($decoded, JSON_UNESCAPED_UNICODE);
                } else {
                    $responseData = mb_substr($responseContent, 0, 1000); // 限制长度
                }
            } catch (Throwable $e) {
                $responseData = '无法解析响应内容';
            }
        }
        
        // 获取设备类型
        $userAgent = $request->server('HTTP_USER_AGENT');
        $deviceType = $this->getDeviceType($userAgent);
        
        // 获取错误信息
        $errorMsg = '';
        if ($responseCode >= 400) {
            $errorMsg = $responseData;
        }
        
        // 判断状态
        $status = ($responseCode >= 200 && $responseCode < 400) ? 1 : 0;
        
        // 创建日志记录
        self::create([
            'method'         => $method,
            'url'            => $url,
            'path'           => $path,
            'query_string'   => $queryString,
            'request_data'   => $requestData,
            'response_code'  => $responseCode,
            'response_data'  => $responseData,
            'error_msg'      => $errorMsg,
            'execution_time' => $executionTime,
            'ip'             => $request->ip(),
            'useragent'      => $userAgent,
            'device_type'    => $deviceType,
            'status'         => $status
        ]);
    }
    
    /**
     * 根据User-Agent获取设备类型
     * @param string $userAgent
     * @return string
     */
    protected function getDeviceType(string $userAgent): string
    {
        if (preg_match('/(iPad|tablet)/i', $userAgent)) {
            return 'tablet';
        } elseif (preg_match('/(iPhone|Android|Mobile|MicroMessenger)/i', $userAgent)) {
            return 'mobile';
        } else {
            return 'desktop';
        }
    }

    /**
     * 获取执行时间属性
     * @param mixed $value
     * @return float|null
     */
    public function getExecutionTimeAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }
}