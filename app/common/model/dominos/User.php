<?php

namespace app\common\model\dominos;

use think\Model;

/**
 * User
 */
class User extends Model
{
    // 表名
    protected $name = 'dominos_user';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'createTime' => 'timestamp:Y-m-d H:i:s',
    ];


    public function proxyip(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\common\model\Proxyip::class, 'proxyip_id', 'id');
    }
}