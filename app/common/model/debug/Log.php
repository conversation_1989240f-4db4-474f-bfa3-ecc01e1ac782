<?php

namespace app\common\model\debug;

use think\Model;
use think\facade\Request;

/**
 * 调试日志模型
 */
class Log extends Model
{
    // 表名
    protected $name = 'debug_log';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    protected $updateTime = false;
    protected $createTime = 'create_time';

    /**
     * 数据类型转换
     */
    protected $type = [
        'id'          => 'integer',
        'line'        => 'integer',
        'user_id'     => 'integer',
        'create_time' => 'integer',
    ];

    /**
     * 记录调试日志
     * @param string $level 日志级别(debug/info/warning/error/critical)
     * @param string $tag 日志标签
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @param int $skipTrace 跳过的堆栈层数
     * @return bool
     */
    public static function record(string $level, string $tag, string $message, array $context = [], int $skipTrace = 0): bool
    {
        try {
            // 获取调用信息
            $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, $skipTrace + 3);
            $caller = $trace[$skipTrace];
            $callerFunction = $trace[$skipTrace + 1] ?? [];
            
            // 获取文件和行号
            $file = $caller['file'] ?? '';
            $line = $caller['line'] ?? 0;
            
            // 获取类名和方法名
            $class = $callerFunction['class'] ?? '';
            $function = $callerFunction['function'] ?? '';
            
            // 如果没有类名，则使用文件名作为模块
            if (empty($class) && !empty($file)) {
                $module = basename($file, '.php');
            } else {
                $module = $class;
            }
            
            // 生成或获取请求ID
            $requestId = Request::header('X-Request-Id') ?: md5(uniqid(microtime(true), true));
            
            // 获取调用堆栈
            $traceStr = self::getTraceAsString($skipTrace + 2);
            
            // 创建日志记录
            self::create([
                'level'      => $level,
                'tag'        => $tag,
                'module'     => $module,
                'function'   => $function,
                'line'       => $line,
                'message'    => $message,
                'context'    => is_array($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : (string)$context,
                'trace'      => $traceStr,
                'request_id' => $requestId,
                'user_id'    => self::getCurrentUserId(),
                'ip'         => Request::ip(),
            ]);
            
            return true;
        } catch (\Throwable $e) {
            // 记录日志失败不应影响正常流程
            trace('调试日志记录失败：' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * 获取格式化的调用堆栈
     * @param int $skip 跳过的堆栈层数
     * @return string
     */
    protected static function getTraceAsString(int $skip = 0): string
    {
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);
        
        // 移除指定数量的堆栈
        for ($i = 0; $i < $skip; $i++) {
            array_shift($trace);
        }
        
        $result = [];
        foreach ($trace as $i => $t) {
            $result[] = sprintf(
                "#%d %s%s%s: %d",
                $i,
                isset($t['class']) ? $t['class'] : '',
                isset($t['type']) ? $t['type'] : '',
                $t['function'] ?? '',
                $t['line'] ?? 0
            );
        }
        
        return implode("\n", $result);
    }
    
    /**
     * 获取当前用户ID
     * @return int
     */
    protected static function getCurrentUserId(): int
    {
        try {
            // 尝试获取管理员ID
            if (session('?admin')) {
                return session('admin.id') ?: 0;
            }
            
            // 尝试获取用户ID
            if (session('?user')) {
                return session('user.id') ?: 0;
            }
            
            return 0;
        } catch (\Throwable $e) {
            return 0;
        }
    }
}

// // 基本用法 - 只记录消息（默认为info级别）
// debug_log('这是一条测试消息');

// // 带标签
// debug_log('订单处理成功', 'order');

// // 带上下文数据
// debug_log('用户登录', ['user_id' => 123, 'ip' => '***********']);

// // 指定日志级别
// debug_log('系统错误', 'system', 'error');

// // 带上下文数据并指定日志级别
// debug_log('支付失败', ['order_id' => 'P123456', 'amount' => 100], 'warning');