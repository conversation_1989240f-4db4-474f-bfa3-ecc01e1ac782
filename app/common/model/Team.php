<?php

namespace app\common\model;

use think\Model;

/**
 * Team
 */
class Team extends Model
{
    // 表名
    protected $name = 'team';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'config' => 'json',
    ];


    public function getConfigAttr($value): array
    {
        return !$value ? [] : json_decode($value, true);
    }

    public function user(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\User::class, 'user_id', 'id');
    }
}