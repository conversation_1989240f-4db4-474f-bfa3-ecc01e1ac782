<?php

namespace app\common\middleware;

use Closure;
use Throwable;
use think\facade\Config;
use app\common\model\api\Log as ApiLogModel;

/**
 * API日志中间件
 */
class ApiLog
{
    /**
     * 写入API日志
     * @throws Throwable
     */
    public function handle($request, Closure $next)
    {
        // 记录开始时间
        $startTime = microtime(true);
        
        // 执行请求
        $response = $next($request);
        
        // 检查是否需要记录日志
        if (Config::get('buildadmin.auto_write_api_log', true)) {
            try {
                // 获取响应内容
                $responseContent = $response->getContent();
                $responseCode = $response->getCode();
                
                // 计算执行时间
                $executionTime = microtime(true) - $startTime;
                
                // 记录日志
                ApiLogModel::instance()->record($responseContent, $responseCode, $executionTime);
            } catch (Throwable $e) {
                // 日志记录失败不影响正常请求
                trace('API日志记录失败：' . $e->getMessage(), 'error');
            }
        }
        
        return $response;
    }
} 