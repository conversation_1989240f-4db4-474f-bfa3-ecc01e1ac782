<?php

namespace app\common\middleware;

use Closure;
use Throwable;
use think\facade\Config;
use app\common\model\mce\UserLog as UserLogModel;

class UserLog
{
    /**
     * 写入用户日志
     * @throws Throwable
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        if (($request->isPost() || $request->isDelete()) && Config::get('mce.auto_write_user_log')) {
            UserLogModel::instance()->record();
        }
        return $response;
    }
}