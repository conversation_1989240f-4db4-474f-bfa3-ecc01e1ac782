<?php

namespace app\common\service;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Queue;
use think\facade\Db;
use modules\plantask\library\Task;
use extend\ba\TableManager;

/**
 * 通用数据导出服务
 * php think queue:listen --queue export_data --sleep=1 --tries=1 --timeout=600
 */
class ExportService
{
    /**
     * 任务处理方式
     */
    const TASK_TYPE_QUEUE = 'queue';    // 使用队列任务
    const TASK_TYPE_PLANTASK = 'plantask'; // 使用计划任务
    
    /**
     * 默认任务处理方式
     * 
     * @var string
     */
    private $defaultTaskType = self::TASK_TYPE_QUEUE;
    
    /**
     * 自动生成的字段映射和表头配置
     * 
     * @var array
     */
    private $autoFieldsConfig = [];
    
    /**
     * 初始化导出任务
     *
     * @param string $modelClass 模型类名
     * @param array $columns 要导出的列
     * @param string $filename 文件名
     * @param array $filter 过滤条件
     * @param array $headers 自定义表头 ['column' => '表头名称']
     * @param array $specialFields 特殊字段处理的配置
     * @param string $module 模块名称，用于缓存键前缀
     * @param string $taskType 任务处理方式，支持 'queue'(队列) 或 'plantask'(计划任务)
     * @return array
     */
    public function initExport(string $modelClass, array $columns = [], string $filename = '导出数据', array $filter = [], array $headers = [], array $specialFields = [], string $module = 'default', string $taskType = '')
    {
        // 如果没有指定表头和列，尝试自动获取
        if (empty($headers) || empty($columns)) {
            $this->autoGenerateFieldsConfig($modelClass);
            
            // 如果未指定要导出的列，使用自动生成的所有列
            if (empty($columns)) {
                $columns = array_keys($this->autoFieldsConfig['headers']);
            }
            
            // 如果未指定表头，使用自动生成的表头
            if (empty($headers)) {
                $headers = $this->autoFieldsConfig['headers'];
            }
            
            // 如果未指定特殊字段处理，使用自动生成的特殊字段配置
            if (empty($specialFields)) {
                $specialFields = $this->autoFieldsConfig['specialFields'];
            }
        }
        
        // 生成唯一的导出ID
        $export_id = md5(uniqid('export_', true));
        
        // 将参数存储到缓存
        Cache::set("export_{$module}_params_" . $export_id, [
            'model_class' => $modelClass,
            'columns' => $columns,
            'filename' => $filename,
            'filter' => $filter,
            'headers' => $headers,
            'special_fields' => $specialFields
        ], 3600);
        
        // 设置初始状态
        Cache::set("export_{$module}_status_" . $export_id, 'queued', 3600);

        
        // 如果未指定任务类型，使用默认任务类型
        if (empty($taskType)) {
            $taskType = $this->defaultTaskType;
        }
        
        // 根据任务类型选择处理方式
        if ($taskType === self::TASK_TYPE_QUEUE) {
            // 将任务推送到队列
            Queue::push('\app\common\service\ExportService@processExportQueue', [
                'export_id' => $export_id,
                'module' => $module
            ], 'export_data');
            Log::info('Export task pushed to queue: ' . $export_id);
        } else if ($taskType === self::TASK_TYPE_PLANTASK) {
            // 使用计划任务
            Task::add(
                task_name: '大数据导出任务-'.$filename,
                type: 'method',
                goal: '\app\common\service\ExportService@processExport',
                params: json_encode([['export_id' => $export_id, 'module' => $module]]),
                repeat_times: 1
            );
            Log::info('Export task added to plan task: ' . $export_id);
        } else {
            // 不支持的任务类型，使用默认的队列处理
            Log::warning('Unsupported task type: ' . $taskType . ', using queue instead.');
            Queue::push('\app\common\service\ExportService@processExportQueue', [
                'export_id' => $export_id,
                'module' => $module
            ], 'export_data');
        }

        return [
            'code' => 1,
            'msg' => '导出任务已提交',
            'data' => [
                'export_id' => $export_id
            ]
        ];
    }
    
    /**
     * 自动生成表头和特殊字段配置
     * 
     * @param string $modelClass 模型类名
     * @return void
     */
    private function autoGenerateFieldsConfig(string $modelClass)
    {
        $model = new $modelClass();
        $tableName = $model->getTable();
        $connection = $model->getConnection();
        
        try {
            // 尝试获取表的字段信息
            if (class_exists('\extend\ba\TableManager')) {
                // 使用BuildAdmin的TableManager
                $fields = TableManager::getTableColumns($tableName, true, $connection);
            } else {
                // 使用ThinkPHP原生方法
                $fields = $this->getTableFields($tableName, $connection);
            }
            
            $headers = [];
            $specialFields = [];
            
            foreach ($fields as $field => $comment) {
                // 跳过不需要导出的字段
                if (in_array($field, ['password', 'salt', 'token'])) {
                    continue;
                }
                
                // 表头使用注释，如果没有注释则使用字段名
                $headers[$field] = is_array($comment) ? ($comment['comment'] ?? $field) : ($comment ?: $field);
                
                // 判断特殊字段类型
                if (is_array($comment) && !empty($comment)) {
                    // 处理日期时间字段
                    if ($this->isDatetimeField($field, $comment)) {
                        $specialFields[$field] = [
                            'type' => 'datetime',
                            'format' => 'Y-m-d H:i:s'
                        ];
                    }
                    
                    // 处理可能的枚举字段
                    if ($this->isEnumField($comment)) {
                        $specialFields[$field] = [
                            'type' => 'map',
                            'map' => $this->parseEnumValues($comment)
                        ];
                    }
                }
            }
            
            $this->autoFieldsConfig = [
                'headers' => $headers,
                'specialFields' => $specialFields
            ];
            
        } catch (\Exception $e) {
            Log::error('Auto generate fields config failed: ' . $e->getMessage());
            $this->autoFieldsConfig = [
                'headers' => [],
                'specialFields' => []
            ];
        }
    }
    
    /**
     * 获取表字段信息
     * 
     * @param string $tableName 表名
     * @param string $connection 数据库连接
     * @return array
     */
    private function getTableFields(string $tableName, string $connection = '')
    {
        $fields = [];
        
        try {
            // 获取表字段信息
            if ($connection) {
                $db = \think\facade\Db::connect($connection);
            } else {
                $db = \think\facade\Db::table($tableName);
            }
            
            $tableFields = $db->getFields($tableName);
            
            foreach ($tableFields as $field => $info) {
                $fields[$field] = $info['comment'] ?: $field;
            }
            
            return $fields;
        } catch (\Exception $e) {
            Log::error('Get table fields failed: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 判断是否为日期时间字段
     * 
     * @param string $field 字段名
     * @param array $info 字段信息
     * @return bool
     */
    private function isDatetimeField(string $field, array $info)
    {
        // 根据字段名判断
        $datetimeFields = ['time', 'date', 'datetime', 'create_time', 'update_time', 'delete_time', 'createtime', 'updatetime', 'deletetime'];
        
        foreach ($datetimeFields as $dateField) {
            if (strpos($field, $dateField) !== false) {
                return true;
            }
        }
        
        // 根据字段类型判断
        $type = $info['type'] ?? '';
        return in_array($type, ['datetime', 'timestamp', 'date', 'time']);
    }
    
    /**
     * 判断是否为枚举字段
     * 
     * @param array $info 字段信息
     * @return bool
     */
    private function isEnumField(array $info)
    {
        $type = $info['type'] ?? '';
        return strpos($type, 'enum') === 0 || strpos($type, 'set') === 0 || in_array($type, ['tinyint', 'smallint']) && $info['comment'] && strpos($info['comment'], ':') !== false;
    }
    
    /**
     * 解析枚举值
     * 
     * @param array $info 字段信息
     * @return array
     */
    private function parseEnumValues(array $info)
    {
        $map = [];
        $comment = $info['comment'] ?? '';
        
        // 尝试解析注释中的枚举值，格式如：0:未激活,1:已激活
        if (strpos($comment, ':') !== false && strpos($comment, ',') !== false) {
            $items = explode(',', $comment);
            foreach ($items as $item) {
                if (strpos($item, ':') !== false) {
                    list($key, $value) = explode(':', $item, 2);
                    $map[trim($key)] = trim($value);
                }
            }
        }
        
        return $map;
    }
    
    /**
     * 设置默认任务处理方式
     * 
     * @param string $taskType 任务处理方式
     * @return $this
     */
    public function setDefaultTaskType(string $taskType)
    {
        if (in_array($taskType, [self::TASK_TYPE_QUEUE, self::TASK_TYPE_PLANTASK])) {
            $this->defaultTaskType = $taskType;
        }
        return $this;
    }
    
    /**
     * 查询导出状态
     *
     * @param string $export_id 导出ID
     * @param string $module 模块名称
     * @return array
     */
    public function getExportStatus(string $export_id, string $module = 'default')
    {
        $status = Cache::get("export_{$module}_status_" . $export_id);

        if ($status === 'completed') {
            $downloadUrl = Cache::get("export_{$module}_download_url_" . $export_id);
            $rows = Cache::get("export_{$module}_rows_" . $export_id);
            return [
                'code' => 1,
                'msg' => '导出完成',
                'data' => [
                    'status' => $status,
                    'download_url' => $downloadUrl,
                    'rows' => $rows
                ]
            ];
        } else if ($status === 'processing' || $status === 'queued') {
            $progress = Cache::get("export_{$module}_progress_" . $export_id, 0);
            return [
                'code' => 1,
                'msg' => '正在导出',
                'data' => [
                    'status' => $status,
                    'progress' => $progress
                ]
            ];
        } else if ($status === 'failed') {
            $message = Cache::get("export_{$module}_message_" . $export_id, '未知错误');
            return [
                'code' => 0,
                'msg' => $message
            ];
        } else {
            return [
                'code' => 0,
                'msg' => '导出任务不存在或已过期'
            ];
        }
    }
    
    /**
     * 下载导出文件
     *
     * @param string $export_id 导出ID
     * @param string $module 模块名称
     * @return \think\Response
     */
    public function downloadExport(string $export_id, string $module = 'default')
    {
        $status = Cache::get("export_{$module}_status_" . $export_id);
        
        if ($status !== 'completed') {
            return response('导出尚未完成或已过期', 404);
        }
        
        $filePath = Cache::get("export_{$module}_file_path_" . $export_id);
        
        if (!file_exists($filePath)) {
            return response('导出文件不存在或已被删除', 404);
        }
        
        $filename = Cache::get("export_{$module}_filename_" . $export_id, '导出数据');
        
        return download($filePath, $filename . '.xlsx');
    }


    /**
     * 处理导出任务（Workerman任务）
     * @param string $export_id 导出ID
     * @param string $module 模块名称
     */
    public function processExport($params): bool
    {
        // 兼容字符串格式的JSON参数和数组参数
        if (is_string($params)) {
            $params = json_decode($params, true);
        }
        
        $export_id = $params['export_id'] ?? '';
        $module = $params['module'] ?? 'default';
        
        if (empty($export_id)) {
            Log::error('Export failed: Empty export_id in plan task');
            return false;
        }

        try {
            // 获取参数并执行导出逻辑
            return $this->handleExport($export_id, $module);
        } catch (\Exception $e) {
            // 更新状态为失败
            Cache::set("export_{$module}_status_" . $export_id, 'failed', 3600);
            Cache::set("export_{$module}_message_" . $export_id, '导出失败: ' . $e->getMessage(), 3600);

            Log::error('Export failed: ' . $export_id . ', Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理导出任务（队列任务）
     *
     * @param \think\queue\Job $job 队列任务
     * @param array $data 任务数据
     */
    public function processExportQueue(\think\queue\Job $job, array $data)
    {
        $export_id = $data['export_id'] ?? '';
        $module = $data['module'] ?? 'default';
        
        if (empty($export_id)) {
            $job->delete();
            return;
        }
        
        try {
            // 获取参数并执行导出逻辑
            $this->handleExport($export_id, $module);
        } catch (\Exception $e) {
            // 更新状态为失败
            Cache::set("export_{$module}_status_" . $export_id, 'failed', 3600);
            Cache::set("export_{$module}_message_" . $export_id, '导出失败: ' . $e->getMessage(), 3600);
            
            Log::error('Export failed: ' . $export_id . ', Error: ' . $e->getMessage());
        }
        
        $job->delete();
    }
    
    /**
     * 处理导出逻辑的公共方法
     * 
     * @param string $export_id 导出ID
     * @param string $module 模块名称
     * @return bool 是否成功
     * @throws \Exception 导出过程中的异常
     */
    private function handleExport(string $export_id, string $module): bool
    {
        // 获取参数
        $params = Cache::get("export_{$module}_params_" . $export_id);
        
        if (empty($params)) {
            throw new \Exception('导出参数不存在或已过期');
        }
        
        $modelClass = $params['model_class'];
        $columns = $params['columns'] ?? [];
        $filename = $params['filename'] ?? '导出数据';
        $filter = $params['filter'] ?? [];
        $headers = $params['headers'] ?? [];
        $specialFields = $params['special_fields'] ?? [];
        
        // 更新状态为处理中
        Cache::set("export_{$module}_status_" . $export_id, 'processing', 3600);
        
        // 实例化模型
        $model = new $modelClass();
        
        // 获取查询构建器
        $query = null;
        
        // 检查是否传递了where条件
        if (isset($filter['where']) && is_array($filter)) {
            // 使用传递的查询条件构建查询
            Log::info('Using where condition for export: ' . json_encode($filter));
            $query = $model;
            
            // 应用where条件
            if (!empty($filter['where'])) {
                $query = $query->where($filter['where']);
            }
            
            // 应用别名
            if (!empty($filter['alias'])) {
                $query = $query->alias($filter['alias']);
            }
            
            // 应用排序
            if (!empty($filter['order'])) {
                $query = $query->order($filter['order']);
            }
        } 
        // 否则使用模型的getQuery方法或默认查询构建
        else {
            // 检查模型是否有getQuery方法
            if (method_exists($model, 'getQuery')) {
                $query = $model->getQuery($filter);
                // 记录日志调试筛选条件
                Log::info('Export filter conditions: ' . json_encode($filter));
            } else {
                // 如果没有getQuery方法，构建基本查询
                $query = $this->buildModelQuery($model, $filter);
            }
        }
        
        // 获取总记录数
        $total = $query->count();
        
        if ($total == 0) {
            Cache::set("export_{$module}_status_" . $export_id, 'failed', 3600);
            Cache::set("export_{$module}_message_" . $export_id, '没有符合条件的数据', 3600);
            return false;
        }
        
        // 创建Spreadsheet实例
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置表头
        $headerRow = 1;
        $columnIndex = 1;
        
        // 如果没有指定要导出的列，使用headers中的所有列
        if (empty($columns)) {
            $columns = array_keys($headers);
        }
        
        // 只处理指定的列
        foreach ($columns as $column) {
            if (isset($headers[$column])) {
                $colLetter = $this->getColumnLetter($columnIndex - 1);
                $sheet->setCellValue($colLetter . $headerRow, $headers[$column]);
                $columnIndex++;
            }
        }
        
        // 设置表头样式
        $sheet->getStyle("A1:{$this->getColumnLetter($columnIndex - 1)}1")->getFont()->setBold(true);
        $sheet->getStyle("A1:{$this->getColumnLetter($columnIndex - 1)}1")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        
        // 分批次获取数据
        $batchSize = 1000;
        $currentRow = 2;
        $processedCount = 0;
        
        for ($offset = 0; $offset < $total; $offset += $batchSize) {
            $records = $query->limit($offset, $batchSize)->select();
            
            foreach ($records as $record) {
                $columnIndex = 1;
                
                // 只处理指定的列
                foreach ($columns as $column) {
                    if (isset($headers[$column])) {
                        $value = $record[$column] ?? '';
                        
                        // 处理特殊字段
                        if (isset($specialFields[$column])) {
                            $fieldConfig = $specialFields[$column];
                            
                            if ($fieldConfig['type'] === 'map' && isset($fieldConfig['map'][$value])) {
                                $value = $fieldConfig['map'][$value];
                            } else if ($fieldConfig['type'] === 'datetime' && !empty($value)) {
                                $format = $fieldConfig['format'] ?? 'Y-m-d H:i:s';
                                $value = is_numeric($value) ? date($format, $value) : $value;
                            }
                        }
                        
                        // 获取列坐标
                        $colLetter = $this->getColumnLetter($columnIndex - 1);
                        $cellCoordinate = $colLetter . $currentRow;
                        
                        // 处理数值类型
                        if (is_numeric($value) && strlen($value) >= 15) {
                            $sheet->setCellValueExplicit($cellCoordinate, $value, DataType::TYPE_STRING);
                        } else {
                            $sheet->setCellValue($cellCoordinate, $value);
                        }
                        
                        $columnIndex++;
                    }
                }
                
                $currentRow++;
                $processedCount++;
            }
            
            // 更新进度
            $progress = round(($processedCount / $total) * 100);
            Cache::set("export_{$module}_progress_" . $export_id, $progress, 3600);
        }
        
        // 自动调整列宽
        foreach (range('A', $this->getColumnLetter($columnIndex - 1)) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // 保存文件
        $uploadDir = public_path() . 'storage' . DIRECTORY_SEPARATOR . 'exports' . DIRECTORY_SEPARATOR;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $filename = $filename . '_' . date('YmdHis');

        $filePath = $uploadDir .  $filename . '.xlsx';
        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);
        
        // 生成下载链接
        $downloadUrl = '/storage/exports/' .$filename . '.xlsx';
        
        // 更新状态为完成
        Cache::set("export_{$module}_status_" . $export_id, 'completed', 3600);
        Cache::set("export_{$module}_download_url_" . $export_id, $downloadUrl, 3600);
        Cache::set("export_{$module}_rows_" . $export_id, $processedCount, 3600);
        Cache::set("export_{$module}_file_path_" . $export_id, $filePath, 3600);
        Cache::set("export_{$module}_filename_" . $export_id, $filename, 3600);
        
        Log::info('Export completed: ' . $export_id . ', Rows: ' . $processedCount);
        
        // 释放资源
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        
        return true;
    }
    
    /**
     * 获取Excel列字母
     *
     * @param int $columnIndex 列索引
     * @return string
     */
    private function getColumnLetter($columnIndex)
    {
        if ($columnIndex < 26) {
            return chr(65 + $columnIndex);
        } else {
            $firstLetter = chr(65 + intval($columnIndex / 26) - 1);
            $secondLetter = chr(65 + ($columnIndex % 26));
            return $firstLetter . $secondLetter;
        }
    }
    
    /**
     * 模型查询构建器
     * 
     * @param \think\Model $model 模型实例
     * @param array $filter 过滤条件
     * @return \think\db\Query
     */
    public function buildModelQuery($model, array $filter = [])
    {
        $query = $model;
        
        // 根据过滤条件构建查询
        if (!empty($filter)) {
            // 这里可以根据实际需求实现更复杂的查询构建逻辑
            foreach ($filter as $field => $value) {
                if (is_array($value) && isset($value['op'])) {
                    // 特殊操作符
                    switch ($value['op']) {
                        case 'LIKE':
                            $query = $query->whereLike($field, $value['value']);
                            break;
                        case 'IN':
                            $query = $query->whereIn($field, $value['value']);
                            break;
                        case 'RANGE':
                            if (isset($value['value'][0]) && isset($value['value'][1])) {
                                $query = $query->whereBetween($field, $value['value']);
                            }
                            break;
                        default:
                            break;
                    }
                } elseif (!empty($value) || $value === 0 || $value === '0') {
                    // 普通等于条件
                    $query = $query->where($field, $value);
                }
            }
        }
        
        return $query;
    }

    /**
     * =====================================================================
     * 以下是控制器方法示例，可以复制到您的控制器中使用
     * =====================================================================
     */
    
    /**
     * 控制器示例方法：导出数据
     * 复制此方法到您的控制器中
     */
    public static function controllerExportData()
    {
        // 以下是控制器方法示例代码
        /*
      public function export_data()
    {
        // 获取参数
        $columns = $this->request->post('columns', []);
        $filename = $this->request->post('filename', '卡密数据');
        // 获取任务类型参数，可以是 queue 或 plantask
        $taskType = $this->request->post('task_type', '');

        // 使用当前页面筛选条件
        list($where, $alias, $limit, $order) = $this->queryBuilder();

        // 方法一：全部自动处理（简化版）
        // 自动获取表字段、生成表头和特殊字段处理配置
        $exportService = new \app\common\service\ExportService();
        $result = $exportService->initExport(
            get_class($this->model),
            $columns, // 不传或传空数组会自动导出全部字段
            $filename,
            [
                'where' => $where,
                'alias' => $alias,
                'order' => $order
            ],
            [], // 不传表头会自动从表注释生成
            [], // 不传特殊字段处理会自动生成
            'cardkeys',
            $taskType
        );

        // 方法二：手动定义（完整版）
        // 定义特殊字段处理配置
        $specialFields = [
            'card_type' => [
                'type' => 'map',
                'map' => [
                    '1' => '含优惠劵',
                    '2' => '不含优惠劵',
                    '3' => '主卡慢充',
                    '4' => '副卡慢充',
                ]
            ],
            'status' => [
                'type' => 'map',
                'map' => [
                    '0' => '未激活',
                    '1' => '已激活',
                    '2' => '已退款',
                    '3' => '已失效',
                    '4' => '已提交'
                ]
            ],
            'create_time' => [
                'type' => 'datetime',
                'format' => 'Y-m-d H:i:s'
            ],
            'update_time' => [
                'type' => 'datetime',
                'format' => 'Y-m-d H:i:s'
            ],
            'lastLoginTime' => [
                'type' => 'datetime',
                'format' => 'Y-m-d H:i:s'
            ],
            'refund_time' => [
                'type' => 'datetime',
                'format' => 'Y-m-d H:i:s'
            ]
        ];

        // 定义自定义表头
        $headers = [
            'id' => 'ID',
            'card' => '卡密',
            'phone' => '分配手机号',
            'batch' => '卡密批次',
            'card_type' => '卡密类型',
            'status' => '卡密状态',
            'deviceId' => '设备ID',
            'deviceName' => '设备名称',
            'lastLoginTime' => '最后登录时间',
            'refund_time' => '退卡时间',
            'remark' => '备注',
            'create_time' => '创建时间',
            'update_time' => '更新时间'
        ];

        // 构建模型查询参数
        $modelParams = [
            'where' => $where,
            'alias' => $alias,
            'limit' => null, // 导出时不限制数量
            'order' => $order
        ];

        // 初始化导出服务
        $exportService = new \app\common\service\ExportService();
        
        // 可选：设置默认任务类型
        // $exportService->setDefaultTaskType(\app\common\service\ExportService::TASK_TYPE_PLANTASK);
        
        // 使用ExportService初始化导出
        $result = $exportService->initExport(
            get_class($this->model),
            $columns ?: array_keys($headers), // 如果未指定列，则导出所有列
            $filename,
            $modelParams,
            $headers,
            $specialFields,
            'cardkeys',
            $taskType // 可选参数：指定使用队列任务或计划任务
        );

        // 返回导出ID和查询状态的API
        return json($result);
    }
        */
    }
    
    /**
     * 控制器示例方法：查询导出状态
     * 复制此方法到您的控制器中
     */
    public static function controllerCheckExportStatus()
    {
        // 以下是控制器方法示例代码
        /*
        public function check_export_status()
        {
            $exportId = $this->request->param('export_id');
            
            if (empty($exportId)) {
                return $this->error('导出ID不能为空');
            }
            
            // 实例化导出服务
            $exportService = new \app\common\service\ExportService();
            
            // 获取导出状态
            $result = $exportService->getExportStatus($exportId, '模块名称');
            
            if ($result['code'] === 1) {
                return $this->success($result['msg'], $result['data']);
            } else {
                return $this->error($result['msg']);
            }
        }
        */
    }
    
    /**
     * 控制器示例方法：下载导出文件
     * 复制此方法到您的控制器中
     */
    public static function controllerDownloadExport()
    {
        // 以下是控制器方法示例代码
        /*
        public function download_export()
        {
            $exportId = $this->request->param('export_id');
            
            if (empty($exportId)) {
                return $this->error('导出ID不能为空');
            }
            
            // 实例化导出服务
            $exportService = new \app\common\service\ExportService();
            
            // 下载文件
            return $exportService->downloadExport($exportId, '模块名称');
        }
        */
    }
    
    /**
     * 模型示例方法：getQuery
     * 复制此方法到您的模型中
     */
    public static function modelGetQueryExample()
    {
        // 以下是模型方法示例代码
        /*
        public function getQuery($filter = [])
        {
            $query = $this->alias('a');
            
            // 处理筛选条件
            if (is_array($filter) && !empty($filter)) {
                foreach ($filter as $key => $value) {
                    // 跳过空值和分页参数
                    if ($value === '' || $value === null || in_array($key, ['page', 'limit', 'sort', 'order'])) {
                        continue;
                    }
                    
                    // 处理特殊操作符
                    if (is_array($value) && isset($value['op'])) {
                        switch ($value['op']) {
                            case 'LIKE':
                                $query->where('a.'.$key, 'like', '%'.$value['value'].'%');
                                break;
                            case 'IN':
                                if (is_string($value['value'])) {
                                    $value['value'] = explode(',', $value['value']);
                                }
                                $query->where('a.'.$key, 'in', $value['value']);
                                break;
                            case 'BETWEEN':
                            case 'RANGE':
                                if (is_string($value['value'])) {
                                    $range = explode(',', $value['value']);
                                } else {
                                    $range = $value['value'];
                                }
                                if (count($range) == 2) {
                                    $query->where('a.'.$key, 'between', $range);
                                }
                                break;
                            default:
                                $query->where('a.'.$key, $value['op'], $value['value']);
                                break;
                        }
                    } else if (is_array($value)) {
                        // 处理数组值（默认为IN操作）
                        $query->where('a.'.$key, 'in', $value);
                    } else {
                        // 处理普通相等查询
                        $query->where('a.'.$key, '=', $value);
                    }
                }
            }
            
            return $query;
        }
        */
    }
} 